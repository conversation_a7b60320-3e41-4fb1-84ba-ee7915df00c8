#!/bin/bash

BS_DIR="/usr/local/.browserstack"
source $BS_DIR/mobile/android/common.sh

DEVICE=$1
DEVICEID=$DEVICE
PORT=$2
CHROME_DRIVER_PORT=$3
ANDROID_BOOTSTRAP_PORT=$4
APPIUM_VERSION=$5
SYSTEM_PORT=$(( 8200 + ($PORT - 38080) ))

# Possible substrings: wait, create, kill
TODO=$6

# set chrome driver version according to user caps
CHROME_DRIVER_VERSION=$7

# set automation name and version
AUTOMATION_NAME=$8
AUTOMATION_VERSION=$9

# set log level
LOG_LEVEL=${10}

SERVICE_NAME="appium_$PORT"
SVSCAN_PATH=/service/$SERVICE_NAME
RUN_FILE=$SVSCAN_PATH/run
LOG_FILE="/var/log/browserstack/create_appium_$DEVICE.log"
MAX_RETRIES=25

if [ "$APPIUM_VERSION" = "1.4.16" ]; then
  APPIUM_EXE=/usr/local/.browserstack/appium_"$APPIUM_VERSION"_bstack/bin/appium.js
elif vers_gte "$APPIUM_VERSION" "2.0.0"; then
  APPIUM_EXE=/usr/local/.browserstack/appium_"$APPIUM_VERSION"_bstack/packages/appium/build/lib/main.js
else
  APPIUM_EXE=/usr/local/.browserstack/appium_"$APPIUM_VERSION"_bstack/build/lib/main.js
fi

if [ "$LOG_LEVEL" = "error" ]; then
  APPIUM_LOG_LEVEL=error
else
  APPIUM_LOG_LEVEL=debug
fi

DEFAULT_CAPABILITIES=''
appium_list=("1.6.5" "1.7.1" "1.7.2" "1.8.0" "1.9.1" "1.10.1" "1.11.1" "1.12.1" "1.13.0" "1.14.0" "1.15.0" "1.16.0" "1.17.0" "1.18.0" "1.19.1" "1.20.2" "1.21.0" "1.22.0" "2.0.0" "2.0.1" "2.4.1" "2.6.0" "2.12.1" "2.15.0")
should_add_default_caps=$(echo ${appium_list[@]} | grep -o "$APPIUM_VERSION" | wc -w)

if [ "$should_add_default_caps" -ne 0 ]; then
  DEFAULT_CAPABILITIES="--default-capabilities \"{\\\"systemPort\\\": $SYSTEM_PORT}\""
fi

# When chrome driver version is 2.30 and android version 4.4 start chromedriver_2.30
# In other cases default chrome driver will be used

get_os_version
if [[ "$CHROME_DRIVER_VERSION" == "2.30" && "$OS_VERSION" == "4.4" ]]; then
  CHROME_DRIVER="/usr/local/.browserstack/chromedriver/chromedriver_2.30"
elif [[ "$CHROME_DRIVER_VERSION" == "87.0" ]]; then
  CHROME_DRIVER=$(get_chromedriver_path_samsung_browser)
else
  CHROME_DRIVER=$(get_chromedriver_path)
fi

if vers_gte "$APPIUM_VERSION" "2.0.0"; then
  DEFAULT_CAPABILITIES="--default-capabilities \"{\\\"appium:systemPort\\\": $SYSTEM_PORT, \\\"appium:chromedriverPort\\\": $CHROME_DRIVER_PORT, \\\"appium:chromedriverExecutable\\\": \\\"$CHROME_DRIVER\\\", \\\"appium:udid\\\": \\\"$DEVICE\\\", \\\"appium:suppressKillServer\\\": \\\"true\\\"}\""
fi

log "CHROME_DRIVER $CHROME_DRIVER"

APPIUM_EXE_PATH=/usr/local/.browserstack/appium_$DEVICE
APPIUM_EXE_CURRENT_VERSION_PATH=$APPIUM_EXE_PATH/appium_location
ANDROID_HOME_PATH="/usr/local/.browserstack/android-sdk/"

if vers_gte $APPIUM_VERSION "1.17.0"; then
  JAVA_HOME_PATH=$JAVA_8_HOME_PATH
else
  JAVA_HOME_PATH=$JAVA_7_HOME_PATH
fi

delete_svscan_files() {
  sudo rm -rf $SVSCAN_PATH
}

kill_current_appium() {
  supervise_pid=`ps -ef | grep supervise | grep $SERVICE_NAME | awk '{print $2}'`
  log "supervise_pid: $supervise_pid"
  if [ ! -z "$supervise_pid" ]; then
    if vers_gte "$APPIUM_VERSION" "2.0.0"; then
      for pid in $supervise_pid; do
        log "Terminating process with PID: $pid"
        $PSTREE -p $pid | grep -P -o '\([0-9]+\)' | tr -d '\(\)' | sudo xargs kill -9
      done
    else
      log "deleting supervise and associated node processes"
      $PSTREE -p $supervise_pid | grep -P -o '\([0-9]+\)' | tr -d '\(\)' | sudo xargs kill -9
    fi
  fi
  ps -ef | grep "$PORT" | grep node | awk '{print $2}' | sudo xargs kill -9
}

log() {
  echo "`date -u`: "$@ >> $LOG_FILE
}

write_run_file_content() {
  mkdir -p $SVSCAN_PATH
  mkdir -p $APPIUM_EXE_PATH
  if vers_gte "$APPIUM_VERSION" "2.6.0"; then
    NODE="$NODE_20_PATH/bin/node"
  elif vers_gte "$APPIUM_VERSION" "2.0.1"; then
    NODE="$NODE_16_PATH/bin/node"
  elif [ "$APPIUM_VERSION" = "2.0.0" ]; then
    NODE="$NODE_14_PATH/bin/node" 
  elif vers_gte $APPIUM_VERSION "1.15.0"; then
    NODE="$NODE_12_PATH/bin/node"
  elif [ "$APPIUM_VERSION" = "1.8.0" ] || [ "$APPIUM_VERSION" = "1.9.1" ] || [ "$APPIUM_VERSION" = "1.10.1" ] || [ "$APPIUM_VERSION" = "1.11.1" ] || [ "$APPIUM_VERSION" = "1.12.1" ] || [ "$APPIUM_VERSION" = "1.13.0" ] || [ "$APPIUM_VERSION" = "1.14.0" ]; then
    NODE="$NODE_8_PATH/bin/node"
  else
    NODE="$NODE_6_PATH/bin/node"
  fi


  # The point was to not write to $RUN_FILE everytime
  # RUN_FILE reads $APPIUM_EXE_CURRENT_VERSION_PATH for current appium exe path
  if vers_gte "$APPIUM_VERSION" "2.0.0"; then
    RUN_FILE_CONTENT="#!/bin/bash
LOG_FILE=\"/var/log/browserstack/appium_$DEVICE.log\"
exec 1>>\$LOG_FILE
exec 2>&1
touch \$LOG_FILE
sudo chmod 666 \$LOG_FILE
sudo su - ritesharora -c 'export ANDROID_HOME=\"$ANDROID_HOME_PATH\"; export JAVA_HOME=\"$JAVA_HOME_PATH\"; APPIUM_HOME=/usr/local/.browserstack/appium_"$APPIUM_VERSION"_bstack/packages/appium/$AUTOMATION_NAME/$AUTOMATION_VERSION \"$NODE\" \`cat $APPIUM_EXE_CURRENT_VERSION_PATH\` --port $PORT --log-timestamp --log-no-colors $DEFAULT_CAPABILITIES --base-path=/wd/hub --use-plugins=all'
    "
  else
    RUN_FILE_CONTENT="#!/bin/bash
LOG_FILE=\"/var/log/browserstack/appium_$DEVICE.log\"
exec 1>>\$LOG_FILE
exec 2>&1
touch \$LOG_FILE
sudo chmod 666 \$LOG_FILE
sudo su - ritesharora -c 'export ANDROID_HOME=\"$ANDROID_HOME_PATH\"; export JAVA_HOME=\"$JAVA_HOME_PATH\"; \"$NODE\" \`cat $APPIUM_EXE_CURRENT_VERSION_PATH\` --chromedriver-executable=$CHROME_DRIVER --chromedriver-port $CHROME_DRIVER_PORT --bootstrap-port $ANDROID_BOOTSTRAP_PORT --suppress-adb-kill-server --port $PORT --udid=$DEVICE --log-level=$APPIUM_LOG_LEVEL --log-timestamp --log-no-colors $DEFAULT_CAPABILITIES'
    "
  fi
  if [[ ! -f $RUN_FILE ]] || [[ $(< $RUN_FILE) != "$RUN_FILE_CONTENT" ]]; then
    log "Creating SVScan files"
    echo "$RUN_FILE_CONTENT" > $RUN_FILE  
    return 1;
  fi
}

if [[ $TODO = *"write"* ]]; then
  write_run_file_content
fi

# Doesn't actually launch appium; we rely on svscan for that
if [[ $TODO = *"create"* ]]; then
  log "Ensuring svscan files exists and default appium is selected"
  echo $APPIUM_EXE > $APPIUM_EXE_CURRENT_VERSION_PATH
  write_run_file_content
  if [[ $? == 1 ]]; then
  # We are calling kill already as a next step of create_kill_wait hence not needed here.
    if vers_gte "$APPIUM_VERSION" "2.0.0"; then
      kill_current_appium
    fi
    chmod +x $RUN_FILE
  fi

  chmod -R 777 $APPIUM_EXE_PATH
fi

if [[ $TODO = *"kill"* ]]; then
  log "Kill current running appium"
  kill_current_appium
fi

if [[ $TODO = *"wait"* ]]; then
  for (( i = 1; i <= MAX_RETRIES; i++ )); do
    curl_output=$( curl -Ss -m 2 http://localhost:$PORT/wd/hub/status )
    if [ "$?" = "0" ]; then
      log "Appium port is up $curl_output"
      if [[ $curl_output == *$APPIUM_VERSION* ]]; then
        log "Correct appium version is up"
        exit 0
      else
        if [ $i != $MAX_RETRIES ]; then
          log "Wrong Appium Version is up. Kill and Wait"
          kill_current_appium
        else
          log "Wrong Appium Version is up. Ignoring and starting the session on wrong version"
        fi
      fi
    else
      if ! [[ -x "$RUN_FILE" ]]
      then
          log "File '$RUN_FILE' is not executable"
      fi
      log "Appium is not up. Try: $i"
    fi
    sleep 2
  done

  exit 1
fi

if [[ $TODO = "remove" ]]; then
  chmod -x $RUN_FILE
  delete_svscan_files
  kill_current_appium
fi

exit 0
