require 'dotenv/load'
require_relative '../constants'
require_relative './http_utils'
require "#{BrowserStack::BS_DIR}/mobile/android/lib/custom_exceptions.rb"
require "#{BrowserStack::BS_DIR}/mobile/common/push_to_zombie"
require 'json'
require 'fileutils'
require 'browserstack_logger'
require_relative './utils'
require_relative '../scripts/upload_to_s3'
require_relative '../lib/os_utils'

class DownloadFiles
  class << self
    # This Method Fetches the File from Downloads Folder of Device and Pushes to AWS s3
    def fetch_and_download_files(device_id, params, send_to_bq: false)

      session_id = params["session_id"]
      product = params["product"]

      start_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)

      log(:info, "Started fetch and download files for device_id: #{device_id}")
      begin
        number_of_files = number_of_files_present_in_download_folder(device_id)

        raise DownloadFilesCustomException, :files_not_available if number_of_files == 0

        size_of_file = download_file_size(device_id)

        raise DownloadFilesCustomException, :max_size_limit_error if size_of_file > (params[:max_files_size].to_i)

        file_download_dir = get_download_files_dir(device_id)

        FileUtils.mkdir_p(file_download_dir)

        exit_code, output = adb_file_cmd(
          'pull', device_id, BrowserStack::DOWNLOAD_FILES_DEVICE_DOWNLOAD_PATH.to_s, file_download_dir, timeout: 60
        )

        if exit_code != 0
          log(:error, "session: #{session_id} cannot pull the file from device with #{output}")
          raise DownloadFilesCustomException, :file_adb_pull_failed
        end

        zip_downloaded_files(file_download_dir, session_id)

        if params['should_use_presigned_url'].to_s == 'true'
          params["s3_config"] = JSON.parse(params["s3_config"])

          # Upload to S3
          file_to_upload = File.join(file_download_dir, "#{session_id}_Download_Files.zip")
          upload_url = params["s3_config"]["upload_url"]
          UploadToS3.upload_zip_to_s3_via_presigned_url(file_to_upload, upload_url)

          # set url to download via pusher
          url = params["s3_config"]["download_url"]
        else
          upload(params, File.join(file_download_dir, "#{session_id}_Download_Files.zip"))

          url = get_presigned_url(params[:s3bucket_endpoint], params["s3_config"], 604800)
        end

        log(:info, "Completed fetch and download files in device : #{device_id}")

        time_taken = Process.clock_gettime(Process::CLOCK_MONOTONIC) - start_time
        if send_to_bq
          send_data_to_bq(device_id, session_id, product,
                          'download_files_android', true, time_taken, BrowserStack::DOWNLOAD_FILES_SUCCESS)
        end

        other_params_to_pusher = { meta_data: { download_files_url: url }.to_json }
        notify_to_pusher(params, product, BrowserStack::DOWNLOAD_FILES_SUCCESS, other_params_to_pusher)
        nil
      rescue StandardError => e

        log(:error, "Failed to Download Files: #{e.message} #{e.backtrace.join("\n")}")
        time_taken = Process.clock_gettime(Process::CLOCK_MONOTONIC) - start_time
        send_data_to_bq(device_id, session_id, product,
                        'download_files_android', false, time_taken, e.message.to_s)

        if !e.message.nil? && (e.message.to_s == FILE_DOWNLOAD_ERRORS[:files_not_available] ||
          e.message.to_s == FILE_DOWNLOAD_ERRORS[:max_size_limit_error])
          notify_to_pusher(params, product, e.message, nil)
        else
          notify_to_pusher(params, product, BrowserStack::DOWNLOAD_FILES_FAILURE, nil)
        end
        raise e
      ensure
        remove_download_files_directory(device_id)
      end
    end

    #Checks number of files present in Downloads folder and gives th number in int
    def number_of_files_present_in_download_folder(device_id)
      adb = AndroidToolkit::ADB.new(udid: device_id)
      adb.shell("ls #{BrowserStack::DOWNLOAD_FILES_DEVICE_DOWNLOAD_PATH} | wc -l").to_i
    rescue StandardError => e
      log(:error, "Failed get number of files in a device: #{device_id}, #{e.message} #{e.backtrace.join("\n")}")
      raise e
    end

    # Checks size of the Downloads Folder and gives response in bytes
    def download_file_size(device_id)
      adb = AndroidToolkit::ADB.new(udid: device_id)
      adb.shell("du -sm  #{BrowserStack::DOWNLOAD_FILES_DEVICE_DOWNLOAD_PATH}").split("\t")[0].to_i
    rescue StandardError => e
      log(:error, "Failed to get size of folder in device: #{device_id}, #{e.message} #{e.backtrace.join("\n")}")
      raise e
    end

    def get_presigned_url(path, s3_config, expires_in)
      s3 = Aws::S3::Resource.new(access_key_id: s3_config["s3_access_keyId"],
                                 secret_access_key: s3_config["s3_secret_access_key"],
                                 region: s3_config["s3_region"])

      bucket = s3.bucket(s3_config["s3_bucket"])
      object = bucket.object(path)
      if object.exists?
        url = object.presigned_url(:get, expires_in: expires_in).to_s
        log(:info, "presigned url generated #{url} for path: #{path}")
        url
      else
        raise "Aws Bucket Object path does not exist"
      end
    end

    # Upload files to AWS S3 bucket
    def upload(params, download_zip_path)
      log(:info, "Called upload method with params:#{params}")
      session_id = params[:session_id]
      params["s3_config"] = JSON.parse(params["s3_config"])
      aws_region = if params["s3_config"]["s3_bucket"] == "bs-stag" ||
        params["s3_config"]["s3_region"] == "us-east-1"
                     ""
                   else
                     "-#{params['s3_config']['s3_region']}"
                   end
      s3bucket_endpoint = params[:s3bucket_endpoint]
      s3_url = "https://s3#{aws_region}.amazonaws.com/#{params[:s3_config]['s3_bucket']}/#{s3bucket_endpoint}"

      region = if params["s3_config"]["s3_bucket"] == "bs-stag" || params["s3_config"]["s3_region"] == "us-east-1"
                 nil
               else
                 (params["s3_config"]["s3_region"]).to_s
               end
      log(:info, "Region is #{region}")
      ret, error = UploadToS3.upload_file_to_s3( #The Download would get deleted after 24 hrs from s3
        params["s3_config"]["s3_access_keyId"],
        params["s3_config"]["s3_secret_access_key"],
        "application/zip",
        download_zip_path,
        "private",
        s3_url,
        region,
        300
      )

      unless ret || error.empty?
        log(:error, "Failed to upload file to s3 error: #{error}")
        raise "Error while uploading Download files to S3: #{error}"
      end
      log(:info, "Successfully uploaded Download files to s3")
      true
    end

    #  Zip the Folder downloaded and remove the main file or source folder of the created zip file
    def zip_downloaded_files(path, session_id)
      zip_name = "#{session_id}_Download_Files.zip"
      cmd = "cd #{path} && zip -r #{zip_name} ./Download/"
      execute_command(cmd)
      true
    end

    def execute_command(cmd, should_raise_error: true)
      result, status = OSUtils.execute(cmd, true)
      if should_raise_error && status != 0
        raise "#{BrowserStack::DOWNLOAD_FILES_TAG} Failed to execute command
        ( non-zero exit code ): #{cmd},result: #{result}, status: #{status}"
      end

      result
    end

    def log(level, message)
      params = { subcomponent: BrowserStack::DOWNLOAD_FILES_TAG }
      BrowserStack.logger.send(level.to_sym, message, params)
    end

    # using sudo as adb pull creates a diretory with root access not with ritesharora
    def remove_download_files_directory(device_id)
      path = get_download_files_dir(device_id)
      execute_command("sudo rm -rf #{path}")
      true
    end

    def notify_to_pusher(params, product, message, other_params_to_pusher)
      log(:info, "pusheer method called with params:#{params}  with product: #{product} with message :#{message}")

      if product == "app_live_testing"
        notify_pusher_app_live(message, params, other_params_to_pusher)
      else
        notify_pusher_live(message, params, other_params_to_pusher)
      end
      true
    end

    def get_download_files_dir(device_id)
      File.join(BrowserStack::DOWNLOAD_FILES_DIR, device_id)
    end

    def send_data_to_bq(device, session, product, action, success, time_taken, error_message = nil)
      message = if [BrowserStack::FILE_DOWNLOAD_ERRORS[:files_not_available],
                    BrowserStack::FILE_DOWNLOAD_ERRORS[:max_size_limit_error],
                    BrowserStack::DOWNLOAD_FILES_SUCCESS, nil].include?(error_message)
                  error_message
                else
                  BrowserStack::DOWNLOAD_FILES_FAILURE
                end
      eds = EDS.new({}, BrowserStack.logger)
      zombie_push('android', "#{action}-fail", error_message, nil, nil, device, session) unless success
      data = {
        event_name: action,
        product: product,
        os: 'Android',
        team: 'device_features',
        event_json: {
          session_id: session,
          device: device,
          success: success,
          time_taken: time_taken,
          message: message
        }
      }
      eds.push_logs("web_events", data)

      true
    rescue StandardError => e
      log(:error,
          "Failed to send data to bq, for device: #{device}, session: #{session}, product:
         #{product}, action: #{action}, success: #{success}, time_taken: #{time_taken}, error_message:
         #{error_message}")

      false
    end
  end
end
