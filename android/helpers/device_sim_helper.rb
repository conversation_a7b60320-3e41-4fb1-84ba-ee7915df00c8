require_relative '../../common/push_to_zombie'
require_relative '../models/device_state'
require_relative '../constants'
require_relative '../version_managers/device_owner_manager'
require_relative '../lib/os_utils'
require_relative 'utils'
require_relative 'uiautomation_helper'
require_relative '../lib/custom_exceptions'

require 'android_toolkit'
require 'yaml'
require 'set'

class DeviceSIMHelper # rubocop:disable Metrics/ClassLength
  MACHINE_IP = "/usr/local/.browserstack/whatsmyip".freeze

  def initialize(params)
    @params = params
    @logger = params[:logger] || BrowserStack.logger
    @adb = AndroidToolkit::ADB.new(udid: params[:device], path: BrowserStack::ADB)
    @device_owner_manager = DeviceOwnerManager.new(params[:device], @logger)
    @eds_obj = EDS.new({}, @logger)
    @eds_event_type = "web_events"
    @device_obj = BrowserStack::AndroidDevice.new(params[:device], "DeviceSimHelper", @logger)
    @device_config = @device_obj.device_config
    @machine_ip = begin
      File.read(MACHINE_IP)
    rescue StandardError
      ""
    end
  end

  def sms_app_bundle
    @sms_app_bundle ||= @adb.shell(
      "dumpsys role | grep -C 1 \"android.app.role.SMS\" | grep \"holders\""
    ).split("=")[1].chomp
  end

  def self.public_sim?(device)
    return false unless File.exist?(BrowserStack::ANDROID_SIM_DEVICES_FILE)

    devices_json = JSON.parse(File.read(BrowserStack::ANDROID_SIM_DEVICES_FILE))
    devices_json.key?(device)
  rescue StandardError => e
    BrowserStack.logger.error "public_sim? has ran into an error: #{e.message}"
    false
  end

  def self.private_sim?(device)
    DeviceState.new(device).sim_info_file_present? && dedicated_device?(device)
  rescue StandardError => e
    BrowserStack.logger.error "private_sim? has ran into an error: #{e.message}"
    false
  end

  def self.sim?(device)
    public_sim?(device) || private_sim?(device)
  rescue StandardError => e
    BrowserStack.logger.error "sim? has ran into error for Device #{device} with error #{
      e.message} error_trace: #{e.backtrace.join("\n")}"
    false
  end

  def self.sim_info_file_present?(device, carrier_index)
    File.exist?("#{BrowserStack::STATE_FILES_DIR}/#{BrowserStack::ANDROID_SIM}_#{device}_#{carrier_index}.yml")
  end

  def self.sim_info_files(device)
    Dir.glob("#{BrowserStack::STATE_FILES_DIR}/#{BrowserStack::ANDROID_SIM}_#{device}_*.yml").sort
  end

  def self.get_sim_info_from_config(device)
    sim_info = JSON.parse(File.read(BrowserStack::ANDROID_SIM_DEVICES_FILE))[device]
    return sim_info unless sim_info.nil?

    JSON.parse(DeviceState.new(device).read_sim_info_file)
  rescue StandardError => e
    BrowserStack.logger.error("Failed to get sim info from config, error: #{e.message}, #{e.backtrace.join("\n")}")
    {}
  end

  def self.dedicated_device?(device)
    File.exist?("#{BrowserStack::STATE_FILES_DIR}/dedicated_device_#{device}")
  end

  def self.get_bars_from_dbm_value(dbm_value)
    # Calculate bars based on dBm value
    bars = case dbm_value
           when -130..-111 then 0
           when -110..-101 then 1
           when -100..-86  then 2
           when -85..-71   then 3
           when -70..-56   then 4
           when -55..0 then 5
           else "Unknown"
           end
  end

  def self.fetch_sim_prop(device, prop, service_call, num = 0, slot = 0)
    android_device = BrowserStack::AndroidDevice.new(device, "DeviceSimHelper", BrowserStack.logger)
    # Xiaomi Redmi Note 9
    slot = android_device.model == "M2003J15SC" && slot == 2 && num == 4 ? 3 : slot
    adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)

    match_alphabets = false
    match_alphabets = true if num == 12 && dedicated_device?(device)
    value = ""

    device_owner_manager = DeviceOwnerManager.new(device, BrowserStack.logger)
    # for devices where device owner privilege is present sim info (carrier, imei, imsi, iccid and phone number)
    # are extracted and stored in device owner application files)
    if android_device.supports_device_owner? && device_owner_manager.privileges_enabled?
      adb.am(
        "start -n com.browserstack.deviceowner/com.browserstack.deviceowner.MainActivity"
      )

      value = adb.shell(
        "run-as com.browserstack.deviceowner cat /data/data/com.browserstack.deviceowner/files/#{prop}.prop"
      )
    end

    if value.eql?("") && service_call.to_s.eql?("true")
      value = iphonesubinfo_command(device, num, slot, match_alphabets: match_alphabets)
    end

      # for devices where device owner privilege is not present we are extracting phone number using browserstack app
      # and storing in /sdcard
    if value.eql?("") && prop.include?("msisdn")
      value = adb.shell(
        "cat /sdcard/#{prop}.prop"
      )
    end
    value
  rescue StandardError => e
    BrowserStack.logger.error "Failed to fetch sim_info or not a sim device, error: #{e.message}"
    ""
  end

  def self.fetch_sim_signal_strength(device, carrier, sim_slot)
    adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
    android_device = BrowserStack::AndroidDevice.new(device, "DeviceSimHelper", BrowserStack.logger)
    # For Android 10 and above, the command to fetch signal strength differs
    command = if android_device.os_version.to_i >= 10
      # Below command fetches the RSRP value from SignalStrength object which is LTE signal strength
                "dumpsys telephony.registry | grep -E 'mSignalStrength' | grep -oE 'rsrp=[0-9-]+' | cut -d= -f2 "
              else
      # Below command fetches the dBm value from SignalStrength object which is GSM signal strength
                "dumpsys telephony.registry | grep -E 'mSignalStrength' | cut -d' ' -f14 "
              end
    dbm_values = adb.shell(command).strip.split("\n").map(&:to_i)

    if dbm_values.empty?
      BrowserStack.logger.info "Unable to retrieve signal strength for carrier #{carrier}, SIM slot #{sim_slot}"
      return { "Bars" => "Unknown", "dBm" => "N/A" }
    end

    # Extract the dBm value based on sim_slot
    dbm_value = sim_slot == 1 ? dbm_values.first : dbm_values.last

    bars = get_bars_from_dbm_value(dbm_value)

    # Send data to BQ, every time signal strength is fetched
    signal_strength_data =
      {
        "carrier" => carrier,
        "signal_strength" => "#{bars} bars", # "signal_strength": "4 bars" matching iOS format
        "dbm" => dbm_value,
        "sim_slot" => sim_slot,
        "dedicated_device" => dedicated_device?(device)
      }.to_json

    zombie_push("android", "sim-signal-strength", "", "", signal_strength_data, device)

    { "Bars" => bars, "dBm" => dbm_value }

  rescue StandardError => e
    BrowserStack.logger.error "Failed to fetch signal strength, error: #{e.message}"
    { "Bars" => "Unknown", "dBm" => "N/A" }

  end

  def self.check_update_signal_strength(device, carrier, sim_slot)
    # Construct file path
    file_path = "#{BrowserStack::STATE_FILES_DIR}/android_sim_#{device}_#{sim_slot - 1}.yml"
    return nil unless File.exist?(file_path) # Return nil if file doesn't exist

    existing_data = YAML.load_file(file_path)
    # Check if carrier and sim_slot match
    if existing_data['carrier'] == carrier && existing_data['sim_slot'] == sim_slot
      last_updated_signal_strength = existing_data['last_updated']
      signal_strength = existing_data['signal_strength']

      # Update signal_strength only if the time difference is greater than 1 hour or last_updated_signal_strength is nil
      if last_updated_signal_strength.nil? || (Time.now.to_i - last_updated_signal_strength.to_i) > 3600
        signal_strength = fetch_sim_signal_strength(device, carrier, sim_slot)
        if signal_strength
          BrowserStack.logger.info "Signal Strength For [Sim : #{sim_slot}, Carrier : #{carrier}] : #{signal_strength}"
        end
        last_updated_signal_strength = Time.now.round
      end

      return { last_updated_signal_strength: last_updated_signal_strength, signal_strength: signal_strength }
    end

    nil # Return nil if no match
  rescue StandardError => e
    BrowserStack.logger.error "Failed to check or update signal strength, error: #{e.message}"
    nil
  end

  def self.sim_info(device, force_update: false)
    force_update = true if dedicated_device?(device)
    is_sim_device = sim?(device)
    sim_info_from_state_file = {}
    sim_details = []
    sim_info_from_config = {}
    return sim_details unless is_sim_device

    sim_info_files = sim_info_files(device)
    sim_info_files.each do |sim_info_file|
      sim_info_from_state_file = begin
        YAML.load_file(sim_info_file) || {}
      rescue StandardError
        force_update = true
        {}
      end
      sim_details.push(sim_info_from_state_file)
    end
    force_update = true if sim_info_files.empty? # Update sim info if device has sim but still there is no sim info file

    return sim_details unless force_update

    adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
    new_sim_details = []

    sim_info_from_config = get_sim_info_from_config(device)

    sim_info_from_config.each do |carrier_index, sim_info|
      new_sim_detail = {}
      #Always take carrier and phone number from latest config
      new_sim_detail['carrier'] = sim_info["carrier"]
      new_sim_detail['phone_number'] = sim_info["phone_number"]

      new_sim_detail['sim_slot'] = carrier_index.to_i + 1
      # Use older imei/iccid if available
      new_sim_detail['imei'] = fetch_sim_prop(device, "imei-#{carrier_index}", true, 4, new_sim_detail['sim_slot'])

      new_sim_detail['iccid'] = fetch_sim_prop(device, "iccid-#{carrier_index}", true, 12, new_sim_detail['sim_slot'])

      File.write("#{BrowserStack::STATE_FILES_DIR}/android_sim_#{device}_#{carrier_index}.yml", new_sim_detail.to_yaml)
      new_sim_details.push(new_sim_detail)
    end
    new_sim_details
  rescue StandardError => e
    BrowserStack.logger.error "sim_info failed with error : #{e.message} #{e.backtrace.join("\n")} "
    []
  end

  def self.disable_sim(device, logger: BrowserStack.logger, from_post_cleanup: false)
    adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)
    obj = new({ device: device, logger: logger })
    obj.toggle_sim_state(0, 0, from_post_cleanup: from_post_cleanup)
    adb.shell("rm /sdcard/public_sim_session")
  rescue StandardError => e
    logger.error "Failed to disable sim, error: #{e.message}"
  end

  def self.iphonesubinfo_command(device, service_call_num, sim_slot, match_alphabets: false)
    adb = AndroidToolkit::ADB.new(udid: device, path: BrowserStack::ADB)

    # Sample raw result
    # 'Result: Parcel(
    # 0x00000000: 00000000 0000000f 00350033 00380032 '........*******.'
    # 0x00000010: 00360031 00300039 00350032 00390034 '*******.*******.'
    # 0x00000020: 00380032 00000039                   '2.8.9...        ')'
    regex = /'([.\d ]*)'/
    regex = /'([a-zA-Z.\d ]*)'/ if match_alphabets
    raw_result = adb.service('call', 'iphonesubinfo', service_call_num, "i32 #{sim_slot - 1}", timeout: '')
    BrowserStack.logger.info "Parsing Output With Regex: #{regex}"
    output = raw_result.scan(regex).join.strip.delete('.')
    output = '' if match_alphabets && !output.nil? && output.chars.all? { |char| char == 'F' }
    output
  end

  def self.update_existing_sim_details(sim_info_file, sim_details, device)
    existing_content = File.read(sim_info_file)
    existing_sim_details = JSON.parse(existing_content)

    sim_details.each do |sim_detail|
      sim_slot = sim_detail["sim_slot"]
      existing_sim_detail = existing_sim_details.find { |detail| detail["sim_slot"] == sim_slot }

      if existing_sim_detail
        existing_sim_detail.merge!(sim_detail)
      else
        existing_sim_details << sim_detail
      end
    end

    File.write(sim_info_file, existing_sim_details.to_json)
    BrowserStack.logger.info("Sim details updated for device #{device}.")
  end

  def self.create_new_sim_details(sim_info_file, sim_details, device)
    FileUtils.touch(sim_info_file)
    File.write(sim_info_file, sim_details.to_json)
    BrowserStack.logger.info("Sim details added for device #{device}")
  end

  def self.validate_sim_details(sim_details)
    required_fields = ["sim_slot", "sim_id", "carrier", "imei", "sim_type", "phone_number"]
    sim_details.each do |sim_detail|
      unless %w[1 2].include?(sim_detail["sim_slot"].to_s)
        raise SimValidationError, 'Invalid sim_slot. It must be 1 or 2.'
      end

      missing_fields = required_fields - sim_detail.keys
      unless missing_fields.empty?
        raise SimValidationError, "Some mandatory fields are missing: #{missing_fields.join(', ')}."
      end
    end
  end

  def validate(action)
    case action
    when "toggle_sim_state_on"
      val = @adb.shell("cat /sdcard/sim_state.prop")
      return true if val.to_s.eql?("1")
    when "toggle_sim_state_off"
      val = @adb.shell("cat /sdcard/sim_state.prop")
      return true if val.to_s.eql?("0")
    when "call_log_cleanup"
      val = @adb.shell("content query --uri content://call_log/calls")
      return true if val.chomp.eql?("No result found.")
    when "sms_log_cleanup"
      val = @adb.shell("content query --uri content://sms")
      return true if val.chomp.eql?("No result found.")
    else
      return false
    end
    false
  end

  def toggle_sim_state(sim_slot, state, from_post_cleanup: false)
    start_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)
    status = ""
    begin
      toggle_via, intent, = get_rule_for_sim_step(@params[:device], "sim_enablement_disablement")
      @logger.info "Received toggle_via: #{toggle_via}, intent: #{intent}"

      if toggle_via == "ui_automation"
        status = "fail"
        @logger.info "Toggling SIM state via UI Automation"

        toggled_succesfully = toggle_sim_state_via_ui_automation(intent, sim_slot, state,
                                                                 from_post_cleanup: from_post_cleanup)

        @logger.info "Toggled SIM state via UI Automation, toggled_succesfully: #{toggled_succesfully}"

        status = "pass" if toggled_succesfully
        return toggled_succesfully
      end

      package_path = @adb.shell("pm path com.android.browserstack").split(":")[1].chomp
      method_name = "setSimPowerStateForSlot"
      @adb.shell(
        "CLASSPATH=#{package_path} app_process / com.android.browserstack.Main #{method_name} #{sim_slot} #{state}"
      )
      if state.eql?("1") && validate("toggle_sim_state_on") || state.eql?("0") && validate("toggle_sim_state_off")
        status = "pass"
        true
      end
    rescue StandardError => e
      zombie_push("android", "toggle-sim-state-failure", e.message, "", "", @params[:device], @params[:session_id])
      @logger.error(
        "Error in toggling sim state for device #{@params[:device]} session id #{
          @params[:session_id]} #{e.message} #{e.backtrace.& join("\n")}"
      )
      status = "fail"
      false
    ensure
      time_taken = Process.clock_gettime(Process::CLOCK_MONOTONIC) - start_time
      send_data_to_eds("toggle-sim-state", time_taken, status, additional_data: { "toggled_via" => toggle_via })
    end
  end

  def toggle_sim_state_via_ui_automation(intent, sim_slot, state, from_post_cleanup: false)
    if from_post_cleanup
      @adb.put_setting('system', 'screen_off_timeout', '86400000')
      go_to_home_screen
    end
    @adb.shell('am force-stop com.android.settings')
    @adb.shell("am start #{intent}")
    uiautomation_helper = BrowserStack::UiAutomationHelper.new(@params[:device], @logger)
    status = uiautomation_helper.handle_toggle_sim_state(sim_slot, state)
    if status
      @adb.shell("touch /sdcard/sim_state.prop")
      @adb.shell("echo #{state} > /sdcard/sim_state.prop")
    end
    status
  ensure
    @adb.put_setting('system', 'screen_off_timeout', '0') if from_post_cleanup
  end

  def block_sim_pages
    via, _, blocking_rules = get_rule_for_sim_step(@params[:device], "sim_enablement_disablement")
    @logger.info("Blocking SIM pages via: #{via}, blocking_rules: #{blocking_rules}")
    return unless via == "ui_automation"

    @logger.info "Blocking SIM pages with rules: #{blocking_rules}"

    @adb.shell("touch /data/local/tmp/blocked_fragments")
    @adb.shell("touch /data/local/tmp/extra_blocked_activities")

    blocked_fragments = ""
    blocked_activities = ""

    blocking_rules["blocked_activities"].each do |rule|
      blocked_activities += "#{rule}|"
    end
    blocking_rules["blocked_fragments"].each do |rule|
      blocked_fragments += "#{rule}|"
    end

    blocked_fragments = blocked_fragments[0..-2] unless blocked_fragments.empty?
    blocked_activities = blocked_activities[0..-2] unless blocked_activities.empty?

    @adb.shell("echo \"#{blocked_activities}\" > /data/local/tmp/extra_blocked_activities")
    @adb.shell("echo \"#{blocked_fragments}\" > /data/local/tmp/blocked_fragments")
  rescue StandardError => e
    @logger.error("Error in blocking sim pages: #{e.message}")
  end

  def unblock_sim_pages
    @adb.shell("rm -f /data/local/tmp/blocked_fragments")
    @adb.shell("rm -f /data/local/tmp/extra_blocked_activities")
  rescue StandardError => e
    @logger.error("Error in unblocking sim pages: #{e.message}")
  end

  def setup

    start_setup_device = Process.clock_gettime(Process::CLOCK_MONOTONIC)
    status = ""
    return false unless self.class.sim?(@device_obj.id)

    begin
      @logger.info "SIM setup triggered"
      toggle_sim_state(0, 1)
      @device_owner_manager.enable_sms if @device_owner_manager.privileges_enabled?

      # Disabling outgoing call using Device-Owener for 15+ SIM devices.
      if @device_owner_manager.privileges_enabled? && @device_obj.os_version.to_i >= 15
        @device_owner_manager.disable_outgoing_calls
      end

      # check signal strength and end session if undesirably low
      max_retries = 10
      delay = 4
      retry_count = 0
      begin
        rssi_value = fetch_rssi_value
        raise "bad signal strength" if rssi_value < BrowserStack::ANDROID_SIM_POOR_SIGNAL_THRESHOLD
      rescue StandardError => e
        if retry_count < max_retries
          retry_count += 1
          sleep delay # wait for some time before retrying to connect to carrier
          retry
        else
          raise "bad signal strength"
        end
      end

      telephony_cleanup("session_start") # clear any sms that may arrive after enabling SIM.
      if validate("toggle_sim_state_on") && validate("call_log_cleanup") && validate("sms_log_cleanup")
        status = "pass"
        @adb.shell("touch /sdcard/public_sim_session")
        true
      else
        raise "setup validation failure"
      end
    rescue StandardError => e
      zombie_push("android", "android-sim-setup-failure", e.message, "", "", @params[:device], @params[:session_id])
      @logger.error(
        "Error in setting up sim for device #{@params[:device]} session id#{
          @params[:session_id]} #{e.message}"
      )
      status = "fail"
      raise e
    ensure
      go_to_home_screen
      time_taken = Process.clock_gettime(Process::CLOCK_MONOTONIC) - start_setup_device
      send_data_to_eds("android-sim-setup", time_taken, status)
    end
  end

  def cleanup
    status = ""
    cleanup_setup_device = Process.clock_gettime(Process::CLOCK_MONOTONIC)
    return false unless self.class.sim?(@device_obj.id)
    return false if self.class.private_sim?(@device_obj.id)

    is_sim_session = false

    begin
      @logger.info "SIM Cleanup started"
      unblock_sim_pages

      is_sim_session = begin
        !@adb.shell("ls /sdcard/public_sim_session").strip.empty?
      rescue StandardError
        false
      end

      telephony_cleanup("cleanup") unless validate("sms_log_cleanup") && validate("call_log_cleanup")
      disable_sms

      @logger.info "SIM Cleanup completed"
      status = "pass"
      true
    rescue StandardError => e
      @logger.error "SIM Cleanup failed, #{e.message}, #{e.backtrace.join("\n")}"
      status = "fail"
      raise e
    ensure
      time_taken = Process.clock_gettime(Process::CLOCK_MONOTONIC) - cleanup_setup_device
      send_data_to_eds("android-sim-cleanup", time_taken, status, sim_session: is_sim_session) unless status.to_s.empty?
    end

  end

  def telephony_cleanup(type)
    start_time = Process.clock_gettime(Process::CLOCK_MONOTONIC)
    status = ""
    begin
      @logger.info "Calls and SMS cleanup triggered"
      @adb.shell("pm grant com.android.browserstack android.permission.WRITE_CALL_LOG")
      @adb.shell("appops set com.android.browserstack WRITE_SMS allow")
      @adb.shell("am startservice --user 0 -n com.android.browserstack/.services.TelephonyCleanupService")
      remove_notifications
      clear_sms_app_data
      if validate("call_log_cleanup") && validate("sms_log_cleanup")
        status = "pass"
        true
      else
        raise "telephony cleanup failure"
      end
    rescue StandardError => e
      zombie_push("android", "telephony-cleanup-failure", e.message, "", "", @params[:device], @params[:session_id])
      @logger.error(
        "Error in telephony cleanup for device #{@params[:device]} session id#{
          @params[:session_id]}"
      )
      status = "fail"
      false
    ensure
      time_taken = Process.clock_gettime(Process::CLOCK_MONOTONIC) - start_time
      send_data_to_eds("telephony-cleanup", time_taken, status, type)
    end
  end

  def clear_sms_app_data
    @adb.shell("pm clear #{sms_app_bundle}")
  rescue StandardError => e
    @logger.error("Error in clearing SMS app data and app cache: #{e.message}\n#{e.backtrace.join("\n")}")
    raise e
  end

  def disable_sms
    @device_owner_manager.disable_sms if @device_owner_manager.privileges_enabled?
  end

  def remove_notifications
    dialer_app = @adb.shell("dumpsys role | grep -C 1 \"android.app.role.DIALER\" | grep \"holders\"").split("=")[1]
                     .chomp
    @adb.shell("am force-stop #{sms_app_bundle}")
    @adb.shell("am force-stop #{dialer_app}")
  end

  def fetch_rssi_value
    @adb.shell(
      "dumpsys telephony.registry | grep -i mSignalStrength"
    ).split.grep(/rssi=-/)[0].split("=")[1].to_i
  rescue StandardError => e
    @logger.info "Failed to fetch rssi value, error: #{e.message}, #{e.backtrace.join("\n")}"
    BrowserStack::ANDROID_SIM_POOR_SIGNAL_THRESHOLD - 1
  end

  def send_data_to_eds(event_name, time, event_status, message = "", sim_session: false, additional_data: {})
    data_to_send = {
      session_id: @params[:session_id],
      device_id: @params[:device],
      device_version: @device_obj.os_version,
      device_name: @device_obj.common_name,
      machine_ip: @machine_ip,
      platform: "Android",
      time: time,
      message: message,
      event_status: event_status,
      region: @device_config['region'],
      sub_region: @device_config['sub_region'],
      is_sim_session: sim_session
    }

    data_to_send.merge!(additional_data)

    params = {
      product: "app_live",
      team: "device_features",
      event_json: data_to_send,
      event_name: event_name
    }
    @eds_obj.push_logs(@eds_event_type, params).join
  rescue StandardError => e
    @logger.error "Failed to send data to eds, error: #{e.message}, #{e.backtrace.join("\n")}"
  end
end

def go_to_home_screen
  @adb.shell("input keyevent 3")
  true
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase.to_sym
  device_id = ARGV[1].to_s.strip
  session_id = ARGV[2].to_s.strip
  product = ARGV[3].to_s.strip
  helper = DeviceSIMHelper.new({ device: device_id, session_id: session_id, product: product })
  helper.send(command)
end
