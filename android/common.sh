#!/bin/bash

BS_DIR="/usr/local/.browserstack"
STATE_FILES_DIR="$BS_DIR/state_files"
BUNDLE="/home/<USER>/bin/bundle"
PUSH_TO_INFLUXDB_SCRIPT="$BS_DIR/mobile/android/lib/android_influxdb_client.rb"
CLEANUP_STATS_FILE="/tmp/cleanup_stats_$DEVICEID"
CLEANUP_MOCK_CAMERA_IMAGE="$BS_DIR/mobile/android/helpers/camera_media_injector.rb"
APP_INJECTION_HELPER="$BS_DIR/mobile/android/helpers/app_injection.rb"
DEVICE_PASSCODE_HELPER="$BS_DIR/mobile/android/helpers/device_passcode.rb"
AUDIO_INJECTOR_HELPER="$BS_DIR/mobile/android/helpers/audio_injector.rb"
LOCAL_TESTING_CHROME_EXTENSION_HELPER="$BS_DIR/mobile/android/helpers/local_testing_chrome_extension_helper.rb"
GOOGLE_PAY_HELPER="$BS_DIR/mobile/android/helpers/google_pay_helper.rb"
SET_TIME_HELPER="$BS_DIR/mobile/android/helpers/set_date_time_helper.rb"
DEVICE_SIM_HELPER="$BS_DIR/mobile/android/helpers/device_sim_helper.rb"
POPUP_HELPER="$BS_DIR/mobile/android/helpers/popup_helper.rb"
SCREEN_LOCK_HELPER="$BS_DIR/mobile/android/helpers/screen_lock_helper.rb"
FINGERPRINT_SENSOR_HELPER="$BS_DIR/mobile/android/helpers/fingerprint_sensor_helper.rb"
RTCAPP_RELEASE_HELPER="$BS_DIR/mobile/android/helpers/rtcapp_release_helper.rb"
RESTRICTION_SCRIPTS_MANAGER="$BS_DIR/mobile/android/version_managers/restriction_scripts_manager.rb"
GET_LAST_URL_HELPER="$BS_DIR/mobile/android/helpers/device_browser_history.rb"
ANDROID_DEVICE_HELPER="$BS_DIR/mobile/android/models/android_device.rb"
CHROME_FLAGS_HELPER="$BS_DIR/mobile/android/helpers/chrome_flags_helper.rb"
NEEDS_MANUAL_FACTORY_RESET_FILE="$STATE_FILES_DIR/needs_manual_factory_reset_"$DEVICEID
NEEDS_REBOOT_FILE="$STATE_FILES_DIR/needs_reboot_$DEVICEID"
NEEDS_REBOOT_FOR_INSTALL_FAILURE_FILE="$STATE_FILES_DIR/needs_reboot_for_install_failures_$DEVICEID"
BRT_STARTED_FOR_BSTACK_APPS_FILE="$STATE_FILES_DIR/brt_started_for_bstack_apps_$DEVICEID"
POPUP_HANDLER_HELPER="$BS_DIR/mobile/android/helpers/popup_helper.rb"
UI_AUTOMATION_HELPER="$BS_DIR/mobile/android/helpers/ui_automation_helper.rb"

set +x
export SEL_HOME=$BS_DIR/mobile/android
export WEBRTC=$BS_DIR/webrtc
export SCRIPTS=$SEL_HOME/live/scripts/
export HELPERS="$BS_DIR/mobile/android/helpers"
export USB_VPN="$BS_DIR/usbVpn/"
export COMMON="$BS_DIR/mobile/common"
export ZOMBIE="$COMMON/push_to_zombie.rb"
export CONFIG="$BS_DIR/config"
export DRIVER_ACTIONS="$SEL_HOME/driver_actions.sh"
export DATA_LOCAL_TMP="/data/local/tmp"
export SESSION_DIR="$DATA_LOCAL_TMP/session"
export STATUSBAR_PID_FILE="$DATA_LOCAL_TMP/check_statusbar_pid"
export STATUSBAR_RUN_FILE="$DATA_LOCAL_TMP/check_statusbar"
export ALT_SCREENRECORD="$DATA_LOCAL_TMP/screenrecord"
export MODEL_DATABASE_SCRIPT="${BS_DIR}/mobile/android/scripts/model_database"
export PATH="$BS_DIR/mobile/deps/bin${PATH:+:${PATH}}"

# load .env environment variables
if [ -f $BS_DIR/mobile/.env ]; then
  export $(cat $BS_DIR/mobile/.env | sed 's/#.*//g' | xargs)
fi
# using as a backup for transitional period
export NODE_6_PATH=${NODE_6_PATH:-"/usr/local"}
export NODE_8_PATH=${NODE_8_PATH:-"/home/<USER>/.nvm/versions/node/v8.6.0"}
export NODE_12_PATH=${NODE_12_PATH:-"/home/<USER>/.nvm/versions/node/v12.6.0"}
export ADB=${ADB:-"/usr/local/bin/adb"}
export MITMDUMP=${MITMDUMP:-"/usr/local/.browserstack/mobile/android/mitmdump"}
export QTFASTSTART=${QTFASTSTART:-"qtfaststart"}
export FFMPEG=${FFMPEG:-"ffmpeg"}
export JAVA_7_HOME_PATH=${JAVA_7_HOME_PATH:-"/usr/lib/jvm/jre-1.7.0-openjdk-********-*******.fc20.x86_64/"}
export JAVA_8_HOME_PATH=${JAVA_8_HOME_PATH:-"/usr/lib/jvm/jre-1.8.0-openjdk.x86_64/"}
export JARSIGNER=${JARSIGNER:-"/usr/lib/jvm/java-1.7.0-openjdk-********-*******.fc20.x86_64/bin/jarsigner"}

export PSTREE=${pstree:-"pstree"}

eval $(ruby $BS_DIR/mobile/config/load_yaml.rb component_versions.yml)

set -x
export DATABASE_PATH="${BS_DIR}/mobile_database.sqlite"
echo "[DATABASE_SELECTION] Using database: $DATABASE_PATH"

source $BS_DIR/mobile/android/helpers/version_comparison.sh
source $BS_DIR/mobile-common/mobile_session_info/lib/common-session.sh
export MACHINE_ENV=`cat $BS_DIR/env | tr -d '\n'`
export ip=`cat $BS_DIR/whatsmyip | tr -d '\n'`
STATIC_CONF=$(cat $CONFIG/static_conf.json)
CLS_HOST=$(echo $STATIC_CONF | jq ".cls_url" | tr -d "\r\n\"")
CLS_PORT="41234"
WIFI_SSID=$(echo $STATIC_CONF | jq ".ssid" | tr -d "\"")
WIFI_PASSWORD=$(echo $STATIC_CONF | jq ".wifi_password" | tr -d "\"")
HOSTNAME=`hostname`
RETRY_COUNT=0
DEFAULT_APPIUM=$(cat $BS_DIR/mobile/config/default_appium_version)
deploy_release_version=$(cat $BS_DIR/mobile/.browserstack_build_version.json | jq -r '.tag')
# overwritten in driver/live/app_live/espresso_actions
COMPONENT="common"
STOP_LOG_INTERNET_STATUS="/sdcard/stop_net_check_logging"
FEDORA_VERSION=$(cat /etc/fedora-release | awk '{print $3}')

if [ -f $BS_DIR/dev_hostname ]; then
  export ip=`cat $BS_DIR/dev_hostname`;
fi

# Like echo, but in stderr and takes no arguments.
warn() {
  echo "$@" 1>&2;
}

log_method_start()
{
  method=$1
  echo "================= Starting $method `date -u` ================="
}

log_method_end() {
  method=$1
  echo "================= Done $method `date -u` ================="
}

log_error()
{
	( >&2 echo "ERROR: $@" )
}

is_split_apk() {
  app_path=$1
  #analyse if it is split apk or just apk and return a boolean
  if [[ -d $app_path ]]; then
    is_split_apks="true"
  else
    is_split_apks="false"
  fi
  echo "$is_split_apks"
}

get_base_apk() {
  base_path=$1
  if [[ $(is_split_apk $base_path) == "true" ]]; then
    base_path="$base_path/base.apk"
  fi
  echo $base_path
}

# To change the default appium version, there is a similar function in:
# android/helpers/utils.rb#get_default_appium_version
get_default_appium_version() {
  device_os_version=$1
  if vers_lt "$device_os_version" "5.0"; then
    default_appium_version="1.6.5"
  else
    default_appium_version=$DEFAULT_APPIUM
  fi
  echo $default_appium_version
}

touch_in_session() {
  session_id=$1
  if [ $# -eq 0 ]; then
    session_id=`mobile_session_get "session_id" | tr -d '"'`
  fi
  $ADB -s $DEVICEID shell "echo '$session_id' > /sdcard/in_session"
}

clean_in_session() {
  $ADB -s $DEVICEID shell "rm -f /sdcard/in_session"
}

touch_dont_log_netcheck() {
  $ADB -s $DEVICEID shell "touch '$STOP_LOG_INTERNET_STATUS'"
}

clean_dont_log_netcheck() {
  $ADB -s $DEVICEID shell "rm -f '$STOP_LOG_INTERNET_STATUS'"
}

install_apk() {
  app_path=$1
  DEVICEID=$2

  if vers_gte "$OS_VERSION" "6"; then
      if [[ $(is_split_apk $app_path) == "true" ]]; then
          install_output=$(timeout 100 $ADB -s $DEVICEID install-multiple -r -g -t $app_path/*.apk 2>&1)
      else
          install_output=$(timeout 100 $ADB -s $DEVICEID install -r -g -t $app_path 2>&1)
      fi
  else
      if [[ $(is_split_apk $app_path) == "true" ]]; then
          install_output=$(timeout 100 $ADB -s $DEVICEID install-multiple -r -t $EXTRA_ADB_OPTIONS $app_path/*.apk 2>&1)
      else
          install_output=$(timeout 100 $ADB -s $DEVICEID install -r -t $EXTRA_ADB_OPTIONS $app_path 2>&1)
      fi
  fi
  local exit_code=$?

  if [[ $exit_code -eq 0 ]]; then
    echo $install_output
  else
    echo "$app_path installation failed with exit code: $exit_code, Output: $install_output"
  fi
}

get_chromedriver_path_samsung_browser(){
    get_os_version
    get_device_model
    local all_chromedrivers="/usr/local/.browserstack/deps/chromedriver"
    if vers_lt "$OS_VERSION" "7" || [[ $device_model == 'SM-G610F' ]]; then # Samsung Galaxy J7 Prime
      chromedriver_path="$all_chromedrivers/v96.0.4664.45/chromedriver"
    elif [[ $device_model == 'SM-A105FN' ]] || [[ $device_model == 'SM-A105M' ]]; then # galaxy A10
      chromedriver_path="$all_chromedrivers/v121.0.6100.0/chromedriver"      
    elif major_vers_eq "$OS_VERSION" "7" || major_vers_eq "$OS_VERSION" "8"; then
      chromedriver_path="$all_chromedrivers/v110.0.5481.77/chromedriver"
    else
      chromedriver_path="$all_chromedrivers/v125.0.6422.141/chromedriver"
    fi

    echo "$chromedriver_path"
}

get_chromedriver_path() {
  get_os_version
  get_device_model
  get_session_genre
  local all_chromedrivers="/usr/local/.browserstack/deps/chromedriver"
  chrome_version=`$ADB -s $DEVICEID shell "dumpsys package com.android.chrome" | grep versionName | head -1 | cut -d '=' -f2 | tr -d '\r\n'`
  # Uses the device's os version and installed chrome version to choose the right chromedriver
  if vers_lt "$OS_VERSION" "5"; then
    chromedriver_path="$all_chromedrivers/v80.0.3987.106/chromedriver"
  elif vers_lt "$chrome_version" "96"; then
    chromedriver_path="$all_chromedrivers/v95.0.4638.69/chromedriver"
  elif vers_lt "$chrome_version" "99"; then
    chromedriver_path="$all_chromedrivers/v96.0.4664.45/chromedriver"
  elif vers_lt "$chrome_version" "100"; then
    chromedriver_path="$all_chromedrivers/v99.0.4844.51/chromedriver"
  elif vers_lt "$chrome_version" "101"; then
    chromedriver_path="$all_chromedrivers/v100.0.4896.60/chromedriver"
  elif vers_lt "$chrome_version" "102"; then
    chromedriver_path="$all_chromedrivers/v101.0.4951.41/chromedriver"
  elif vers_lt "$chrome_version" "104"; then
    chromedriver_path="$all_chromedrivers/v103.0.5060.134/chromedriver"
  elif vers_lt "$chrome_version" "105"; then
    chromedriver_path="$all_chromedrivers/v104.0.5112.79/chromedriver"
  elif vers_lt "$chrome_version" "106"; then
    chromedriver_path="$all_chromedrivers/v105.0.5195.52/chromedriver"
  elif vers_lt "$chrome_version" "111"; then
    chromedriver_path="$all_chromedrivers/v108.0.5359.71/chromedriver"
  elif vers_lt "$chrome_version" "113"; then
    chromedriver_path="$all_chromedrivers/v111.0.5563.64/chromedriver"
  elif vers_lt "$chrome_version" "114"; then
    chromedriver_path="$all_chromedrivers/v113.0.5672.63/chromedriver"
  elif vers_lt "$chrome_version" "117"; then
    chromedriver_path="$all_chromedrivers/v114.0.5735.90/chromedriver"
  elif vers_lt "$chrome_version" "120"; then
    chromedriver_path="$all_chromedrivers/v117.0.5938.92/chromedriver"
  elif vers_lt "$chrome_version" "123"; then
    chromedriver_path="$all_chromedrivers/v120.0.6099.109/chromedriver"
  elif vers_lt "$chrome_version" "124"; then
    chromedriver_path="$all_chromedrivers/v123.0.6312.86/chromedriver"
  else
    if [[ "$device_model" == "BON-AL00" ]]; then
      chromedriver_path="$all_chromedrivers/v99.0.4844.51/chromedriver"
    else
      chromedriver_path="$all_chromedrivers/v135.0.7049.114/chromedriver"
    fi
  fi

  echo "$chromedriver_path"
}

function file_mod_time_seconds
{
    if file_mod=$(stat -c %Y -- "$1")
    then
        echo $(( $(date +%s) - $file_mod ))
    else
        return $?
    fi
}

assert_parameters()
{
	function=$1; shift
	count=$1; shift
	if [ "$#" != "$count" ]
	then
		log_error "$function expected $count parameters, got $# - $@"
		return 1
	fi
	i=0
	for p in "$@"
	do
		let i++
		if [ -z "$p" ]; then
			log_error "$function parameter $i is empty - $@"
			return 1
		fi
	done
	return 0
}

json_getprop()
{
	if ! assert_parameters "json_getprop" 2 "$@"
	then
		echo
		return 1
	fi
	file="$1"
	prop="$2"
	if [ ! -s "$file" ]; then
		log_error "json_getprop missing file :$file: for query to :$prop:"
		return 1
	else
		jq ".$prop" "$file" | tr -d '\r\n\t"'
	fi
}

adb_getmodel_impl()
{
        device="${DEVICEID:=$1}"
        timeout 5 $ADB -s "$device" shell getprop "ro.product.model" < /dev/null | head -1 | cut -d'=' -f2 | tr -d '\r\n\t'
}

adb_getprop()
{
	case $# in
	1)
		device="$DEVICEID"
		prop="$1";;
	2)
		device="$1"
		prop="$2";;
	*)
		log_error "adb_getprop: invalid parameters ($#) $@"
		echo
		return;;
	esac
  timeout 4 $ADB -s "$device" shell getprop "$prop" < /dev/null | tr -d '\r\n\t'
}

# if we don't get any logs of ASSERTION ERROR from production, we can replace this with body of adb_getmodel_impl()
adb_getmodel()
{
        device="${DEVICEID:=$1}"
        model=`adb_getmodel_impl "$device"`
        model2=`adb_getprop "$device" "ro.product.model"`
        if [ "$model" != "$model2" ]
        then
                log_error "ASSERTION ERROR: adb_getmodel() :$model: != :$model2:"
        fi
        echo $model
}

get_device_model() {
  if [[ -z "$device_model" ]]; then
    device_model=$(adb_getmodel $DEVICEID)
  fi
}

get_os_version() {
  if [[ -z "$OS_VERSION" ]]; then
    OS_VERSION=`$ADB -s $DEVICEID shell getprop ro.build.version.release | tr -d '\r\n'`
  fi
  if [[ "$OS_VERSION" == "14" ]]; then
    codename=`$ADB -s $DEVICEID shell getprop ro.build.version.release_or_codename | tr -d '\r\n'`
    if [[ "$codename" == "VanillaIceCream" ]]; then
      OS_VERSION="15"
    fi
  fi

  if [[ "$OS_VERSION" == "15" ]]; then
    firmware=`$ADB -s $DEVICEID shell getprop ro.build.version.incremental | tr -d '\r\n'`
    if [[ "$firmware" == "12787338" ]]; then
      OS_VERSION="16"
    fi
  fi
}

get_session_genre() {
  SESSION_GENRE=`mobile_session_get "session_type" | tr -d '"'`
}

# Fetch a boolean property from our mobile database. Requires the following
# global variables:
#
#   $device_model or $DEVICEID
#   $OS_VERSION   [Defaults to the first one in the DB if missing]
#
# It also takes the following arguments:
#
#   $1: The property to fetch
#
# It will both echo true/false and return 0 or 1 to signal the value of the
# property, so it can be used like
#
#   if [[ `device_is_a bsrun_device` -eq true ]]; then
#     echo 'runing root command...'
#   fi
#
# and like
#
#   if device_is_a bsrun_device; then
#     echo 'runing root command...'
#   fi
#
# WARNING: the function employs memoization to be faster (each db access with
# the script costs around 0.30s). The results will be stored in a global
# variable, and the database will only be hit on the first question. The second
# time the value is accessed, the function runs instantly (<0.000s).
declare -A memodict__device_attributes
device_is_a(){
  get_os_version
  local property=$1
  local memoization_key="${device_model}_${OS_VERSION}_${property}"

  # Fetch and store in dict if not there
  if [[ -z ${memodict__device_attributes[$memoization_key]} ]]; then
      # OS_VERSION can be missing, and it will interpolate as an empty string.
      # That is fine, because model_database takes either 2 or 3 args, and
      # picks a random OS_VERSION if none is provided.
      warn "Hitting the database (memoization key ${memoization_key})"
      memodict__device_attributes[$memoization_key]=`cd ${BS_DIR}/mobile; ${BUNDLE} exec ruby ${MODEL_DATABASE_SCRIPT} "${device_model}" ${OS_VERSION} ${property}`
  fi

  if [[ ${memodict__device_attributes[$memoization_key]} = "true" ]]; then
    echo "true"
    return 0
  else
    echo "false"
    return 1
  fi
}

#TODO: Move to DB Flag
device_in_bstack_vpn_list() {
  get_device_model
  get_os_version
  if vers_gte "$OS_VERSION" "14" || major_vers_eq "$OS_VERSION" "10" || major_vers_eq "$OS_VERSION" "12" || (major_vers_eq "$OS_VERSION" "13" && [[ $device_model != "CPH2531" ]] ) ||
  (major_vers_eq "$OS_VERSION" "11" && [[ $device_model != "Pixel 5" ]] ) ; then
    echo "true"
    return "true";
  else
    echo "false"
    return "false";
  fi
}

device_attribute() {
  local device_model=$1
  local os_version=$2
  local property=$3
  local memoization_key="${device_model}_${os_version}_${property}"

  # Fetch and store in dict if not there
  if [[ -z ${memodict__device_attributes[$memoization_key]} ]]; then
      memodict__device_attributes[$memoization_key]=`cd ${BS_DIR}/mobile; ${BUNDLE} exec ruby ${MODEL_DATABASE_SCRIPT} "${device_model}" ${os_version} ${property}`
  fi
  echo ${memodict__device_attributes[$memoization_key]}
}

fetch_props() {
  if [ -z "$model" -o -z "$WIPE" -o -z "$BSRUN" ]
  then
    # The following two lines are crucial. If you try to use get_device_model
    # or something like that firefox doesn't start and the code breaks badly.
    model=$(adb_getmodel "$1")
    device_model=$model
    echo "INFO: fetch_props for $1 of type $model"
    if device_is_a log_tag_wipe_device; then
      WIPE=log.tag.wipe
      BSRUN=log.tag.bsrun
      BSRUN_WAIT=log.tag.bsrun_wait
    else
      WIPE=log.wipe
      BSRUN=log.bsrun
      BSRUN_WAIT=log.bsrun_wait
    fi
  fi
}

LOCK_WAIT_PERIOD=2

lock_state_device()
{
  pid=`bash -c 'echo \$PPID && :'`
  state=$1
  device=$2
  file=/tmp/_"$state"_$device.lock
  while true
  do
    while [ -f $file ]
    do
      echo "INFO: $state - process $0 $pid waiting to lock $file"
      mod=`file_mod_time_seconds $file`
      if [ "$mod" -gt 60 ]
      then
        echo "WARN: $state - $0 $pid found lock from process `cat $file` hanging - removing lock"
        rm -f $file
      else
        sleep $LOCK_WAIT_PERIOD
      fi
    done
    if [ ! -f $file ]
    then
      echo $pid >> $file
      if [ "`cat $file`" == "$pid" ]
      then
        echo "INFO: BEGIN $state in process $pid"
        return 0
      fi
      # conflict - back off
      rm -f $file
      delay=$(( ( RANDOM % LOCK_WAIT_PERIOD ) + 1 ))
      echo "WARN: conflict process $0 $pid failed to lock $file - wait $delay seconds"
      sleep $delay
    fi
  done
}

unlock_state_device()
{
  pid=`bash -c 'echo \$PPID && :'`
  force=
  if [ "$1" == "-f" ]
  then
    force=y
    shift
  fi
  state=$1
  device=$2
  file=/tmp/_"$state"_$device.lock
  if [ ! -z "$force" ]
  then
    echo "WARN: $state - forcing unlock on $file"
    rm -f $file
    return 0
  fi
  if [ -f $file ]
  then
    if [ "`cat $file`" == "$pid" ]
    then
      rm -f $file
      echo "INFO: END $state in process $pid"
      return 0
    else
      echo "ERROR: $state - file $file is not locked by $pid"
    fi
  else
    echo "ERROR: $state - file $file is not locked (file does not exist)"
  fi
  return 1
}

bsrun_wait() {
  DEVICEID="${DEVICEID:=$1}"
  sleep 1
  for j in {1..100}; do
    bsrun_waiting=`$ADB -s $DEVICEID shell getprop $BSRUN_WAIT | tr -d '\r\n'`

    if ! $ADB devices | grep $DEVICEID; then
      # TODO fix me. We need to revisit this flow and make less assumptions
      # Tracked on MOB-2764
      echo "Phone went off usb, assuming it was a factory reset command"
      return 0
    fi

    if [[ "$bsrun_waiting" = 0 ]]
    then
      if [ "$j" -gt 5 ]; then
        if [ "$j" -lt 10 ]
        then
          echo "WARN: bsrun_wait $j seconds"
        else
          echo "WARN: bsrun_wait SLOW $j seconds"
          get_device_model
          $BUNDLE exec $COMMON/push_to_zombie.rb "android" "bsrun-wait-slow" "$DEVICEID" "$device_model ($j)"
          push_to_influxdb_v1 "bsrun-wait-slow" $COMPONENT "bsrun_wait" "$DEVICEID" "false"
        fi
      fi
      return 0
    fi
    sleep 1
  done
  echo "BSRUN failed!"
  get_device_model
  $BUNDLE exec $COMMON/push_to_zombie.rb "android" "bsrun-wait-failed" "$DEVICEID" "$device_model"
  push_to_influxdb_v1 "bsrun-wait-failed" $COMPONENT "bsrun_wait" "$DEVICEID" "true"
  return 126
}

bsrun_force_unlock()
{
   unlock_state_device -f "bsrun" "$DEVICEID"
}

run_as_root() {
  COMMAND_TO_RUN="$1"

  if [ -z "$2" ];
  then
    RETRY_TIMES=1
  else
    RETRY_TIMES="$2"
  fi

  if [ -z "$DEVICEID" ]
  then
    echo "ERROR: run_as_root - DEVICEID not set"
    return 1
  fi

  fetch_props $DEVICEID
  if [[ -z $device_model ]]; then
    get_device_model
  fi
  if [ "$OS_VERSION" == "unknown" ] || [ -z "$OS_VERSION" ]; then
    echo "Dont know OS Version. Trying to fetch from adb"
    get_os_version
  fi

  if ! device_is_a bsrun_device; then
    echo "WARNING: Calling run_as_root on an unsupported device. Ignoring."
    return 1
  fi

  rar_count=0
  while [ $rar_count -le $RETRY_TIMES ]
  do
    lock_state_device bsrun $DEVICEID

    file=/tmp/bsrun_$DEVICEID.sh
    log=$DATA_LOCAL_TMP/bsrun.log
    echo "{" > $file
    echo "echo \"Date before command \`date\`\"" >> $file
    echo "setprop $BSRUN_WAIT 1" >> $file
    echo "echo \"Running command: $COMMAND_TO_RUN\"" >> $file
    if [[ "$@" == "heredoc" ]]
    then
      cat >> $file
    else
      echo "$COMMAND_TO_RUN" >> $file
    fi
    echo "echo \"Date after command \`date\`\"" >> $file
    echo "setprop $BSRUN_WAIT 0" >> $file
    echo "echo \"BSRUN_WAIT prop is: \`getprop $BSRUN_WAIT\`\"" >> $file
    echo "} > $log 2>&1; chown 2000 $log; chmod 777 $log " >> $file

    # Pushing file with our built command to the device
    $ADB -s $DEVICEID push $file $DATA_LOCAL_TMP/bsrun.sh > /dev/null

    $ADB -s $DEVICEID shell setprop $BSRUN_WAIT 1
    # Triggering bsrun.sh to run as root
    $ADB -s $DEVICEID shell setprop $BSRUN 1

    # Waiting for command to finish, bsrun_wait prop should be 0 once the command is done
    bsrun_wait $DEVICEID
    bsrun_failed=$?
    $ADB -s $DEVICEID shell rm $DATA_LOCAL_TMP/bsrun.sh

    outlog=/tmp/bsrun_$DEVICEID_$$.log
    $ADB -s $DEVICEID pull $log $outlog > /dev/null
    cat $outlog
    check_bsrun_logs $outlog

    sudo rm -f $file $outlog

    unlock_state_device bsrun $DEVICEID
    rar_count=$((rar_count+1))
    if [ $bsrun_failed == 0 ]; then break; fi
  done

  if  [ $bsrun_failed != 0 ]; then
      $BUNDLE exec $ZOMBIE "android" "run_as_root-timed-out" "$device_model" "" "$COMMAND_TO_RUN timed out after $rar_count trys" "$DEVICEID"
      bsrun_processes=$($ADB -s $DEVICEID shell "/data/local/tmp/busybox ps aux | grep bsrun | grep -v grep | wc -l")
      if [[ $bsrun_processes -gt 0 ]]; then
        $BUNDLE exec $ZOMBIE "android" "run_as_root-zombie-process" "$device_model" "" "$COMMAND_TO_RUN" "$DEVICEID"
      fi
  fi

  return $bsrun_failed
}

check_bsrun_logs() {
  local logfile=$1
  if grep -Eq "xargs: exec rm: Argument list too long|rm: Unknown option" "$logfile"; then
    # if bsrun logs contains this message then full cleanup failed, mark device offline
    echo "Found rm error message: Full cleanup failed for $DEVICEID"
    touch $NEEDS_MANUAL_FACTORY_RESET_FILE
  fi
}

get_host_region() {
  if [[ -z "$REGION" ]]; then
    CONFIG_REGION=$(json_getprop "$CONFIG/static_conf.json" "region")
    case "$CONFIG_REGION" in
    "eu-west-1") REGION="euw";;
    "eu-central-1") REGION="euc";;
    "us-west-1") REGION="usw";;
    "us-east-1") REGION="use";;
    "ap-south-1") REGION="aps";;
    "ap-southeast-2") REGION="apse";;
    *) echo "ERROR: invalid region :$CONFIG_REGION:";;
    esac
  fi
}

start_uiautomator() {
  get_os_version
  if vers_gte "$OS_VERSION" "11"; then
    $ADB -s $DEVICEID push /usr/local/.browserstack/deps/input_injector/v3/InputInjector.apk /data/local/tmp/InputInjector.apk
    $ADB -s $DEVICEID shell 'CLASSPATH="/data/local/tmp/InputInjector.apk" app_process / com.browserstack.inputinjector.Main &'
  else
    devicelog=$DATA_LOCAL_TMP/uiautomator.log
    $ADB -s $DEVICEID shell <<__EOF
    uiautomator runtest $DATA_LOCAL_TMP/InputInjector.jar -c com.browserstack.inputinjector.Main &
    count=0
    while [ "\$count" -lt 5 ]
    do
      pscount=0
      while [ "\$pscount" -lt 4  ]
      do
        sleep 2
        if $BUSYBOX pgrep uiautomator
        then
          sleep 2
          port_info=\`netstat -nl -t | grep ':1080 '\`
          if [ -z "\$port_info" ]
          then
            $BUSYBOX pgrep uiautomator | $BUSYBOX xargs kill -2
            uiautomator runtest $DATA_LOCAL_TMP/InputInjector.jar -c com.browserstack.inputinjector.Main &
          else
            echo "uiautomator started successfully on the \$count try" > $devicelog
            count=6
          fi
          break
        fi
        pscount=\$((pscount+1))
      done
      if [ "\$pscount" -eq 4 ]
      then
        echo "uiautomator failed to start process" > $devicelog
        break
      fi
      count=\$((count+1))
    done
    if [ "\$count" -eq 5 ]
    then
      echo "uiautomator failed to start correctly after \$count tries" > $devicelog
    fi
__EOF
    result=`$ADB -s $DEVICEID shell "cat $devicelog"`
    cls_log_message "uiautomator_started" "$result"
  fi
}

cls_log_message () {
  # take args: msg(mandatory) error(optional)
  message=$1
  error=${2:-""}

  if [ "$LOG_EVENTS" == "false" ]; then
    echo "$message"
    return
  fi

  file_to_read=$CONFIG/rtc_service_$DEVICEID
  user_id=$(cat $file_to_read | jq ".user_id" | tr -d "\r\n\"")

  genre=$(cat $file_to_read | jq ".genre" | tr -d "\r\n\"")
  if [ -z $genre ] || [[ $genre == "null" ]]; then
    genre=`mobile_session_get "session_type" | tr -d '"'`
  fi

  utc_stamp=$(date -u +"%F %T.%3N UTC")
  case $genre in
    "app_live_testing" )
      session_id=$(cat $file_to_read | jq ".app_live_session_id" | tr -d "\r\n\"")
      read -d '' json_message <<EOF
      {"app_live_session_id" : "$session_id",
      "user_id" : "$user_id",
      "json_data" : {"device_id": "$DEVICEID"},
      "release_version" : "$deploy_release_version",
      "message" : "$message",
      "error" : "$error",
      "product" : "App Live",
      "app" : "android_platform",
      "app_timestamp" : "$utc_stamp"}
EOF
    ;;
    "automate" )
      session_id=`mobile_session_get "session_id" | tr -d '"'`
      read -d '' json_message <<EOF
      {"automate_session_id" : "$session_id",
      "user_id" : "$user_id",
      "json_data" : {"device_id": "$DEVICEID"},
      "release_version" : "$deploy_release_version",
      "message" : "$message",
      "error" : "$error",
      "product" : "Automate",
      "app" : "android_platform",
      "app_timestamp" : "$utc_stamp"}
EOF
    ;;
    "app_automate" )
      session_id=`mobile_session_get "session_id" | tr -d '"'`
      read -d '' json_message <<EOF
      {"app_automate_session_id" : "$session_id",
      "user_id" : "$user_id",
      "json_data" : {"device_id": "$DEVICEID"},
      "release_version" : "$deploy_release_version",
      "message" : "$message",
      "error" : "$error",
      "product" : "App Automate",
      "app" : "android_platform",
      "app_timestamp" : "$utc_stamp"}
EOF
    ;;
    * )
      session_id=$(cat $file_to_read | jq ".live_session_id" | tr -d "\r\n\"")
      read -d '' json_message <<EOF
      {"live_session_id" : "$session_id",
      "user_id" : "$user_id",
      "json_data" : {"device_id": "$DEVICEID"},
      "release_version" : "$deploy_release_version",
      "message" : "$message",
      "error" : "$error",
      "product" : "Live",
      "app" : "android_platform",
      "app_timestamp" : "$utc_stamp"}
EOF
    ;;
  esac
  echo -n $json_message > /dev/udp/$CLS_HOST/$CLS_PORT

}

uptime_params_to_hoothoot() {
  get_host_region
  MEASUREMENT="unique_user_event"
  PRODUCT="live"
  TAG=$(echo $1 | sed 's/ /\\ /g; s/,/\\,/g; s/=/\\=/g')
  if [[ $HOSTNAME == *"browserstack.com"* ]]; then
    out="$MEASUREMENT,product=$PRODUCT,event_type=$TAG value=$2 $(date +%s%N)"
    echo $out > /dev/udp/metrics-$REGION.browserstack.com/25821
  fi
}

failures_to_hoothoot() {
  tag=$1
  value=$2
  device=$3
  get_host_region
  MEASUREMENT="mobile_stats"
  os="realdroid"
  device_name=$(adb_getmodel "$device")
  osversion=$(adb_getprop "$device" ro.build.version.release)
  device_tag_prefix=$(json_getprop $BS_DIR/mobile/android/device_mapping.json "$device_name")
  if [[ -z "$device_tag_prefix" ]] || [[ -z "$osversion" ]];  then
    echo "Insufficient data to push to hoothoot!"
  else
    dtype=$( echo $device_tag_prefix$osversion | tr -d '"')
    out="$MEASUREMENT,os=$os,product=live,terminal_type=${dtype},kind=$tag value=$value $(date +%s%N)"
    echo $out > /dev/udp/metrics-$REGION.browserstack.com/25828
  fi
}

cleanup_stats_to_hoothoot() {
  tag=$1
  cleanup_tag_prefix=$2
  cleanup_os_version=$3
  get_host_region
  MEASUREMENT="mobile_cleanup_stats"
  os="android"
  if [[ -z "$cleanup_tag_prefix" ]] || [[ -z "$cleanup_os_version" ]];  then
    echo "Insufficient data to push to hoothoot!"
  else
    dtype=$( echo $cleanup_tag_prefix$cleanup_os_version | tr -d '"')
    out="$MEASUREMENT,os=$os,terminal_type=${dtype},kind=$tag value=1 $(date +%s%N)"
    echo $out > /dev/udp/metrics-$REGION.browserstack.com/25828
  fi
}

execution_time_to_hoothoot() {
  MEASUREMENT=$1
  value=$2
  device_model=$3
  tag=$4

  get_host_region
  os="android"

  device_model_key=$(echo $device_model | tr ' ' _ | tr - _ | tr -d '"' | tr -d '()')
  device_tag_prefix=$(json_getprop $BS_DIR/mobile/android/device_mapping.json "$device_model_key")
  device_os_version=$(echo $OS_VERSION | tr . "_" | tr -d '"')
  if [ -z "$device_model_key" -o -z "$device_tag_prefix" -o -z "$device_os_version" ]; then
    echo "Insufficient data to push to hoothoot!"
  else
    dtype=$( echo $device_tag_prefix$device_os_version | tr -d '"')
    if [[ -z "$tag" ]]; then
      out="$MEASUREMENT,os=$os,terminal_type=${dtype} value=$value $(date +%s%N)"
    else
      out="$MEASUREMENT,os=$os,terminal_type=${dtype},kind=$tag value=$value $(date +%s%N)"
    fi
    echo $out > /dev/udp/metrics-$REGION.browserstack.com/25828
  fi
}

remove_app() {
  if [[ $IS_DEDICATED_CLEANUP -eq 1 ]]; then
    echo "Skipping remove_app device $DEVICEID"
    return 0
  fi

  $ADB -s $DEVICEID shell pm clear "$1"
  $ADB -s $DEVICEID shell pm uninstall "$1"
}

function fail_cleanup_with_reason() {
  cleanup_failure_reason=$1
  record_cleanup_failure_reason "$cleanup_failure_reason"
  exit 255
}

check_screen_lock () {
  DEVICEID="${DEVICEID:=$1}"
  # This function returns 200 if screen is locked
  wakefulness=$($ADB -s $DEVICEID shell dumpsys power | grep -w "mWakefulness\|getWakefulnessLocked")
  if [[ -z $wakefulness ]]; then
    lockState=$($ADB -s $DEVICEID shell dumpsys power)
    if [[ ! -z $lockState ]]; then
      lockState=$(echo $lockState | grep -w "mWakeLockState\|mUserState" | wc -l)
      if [[ $lockState = 0 ]]; then
        # We use this logline in espresso_helper to assert if screen is locked.
        echo "Screen is locked."
        return 200
      fi
    else
      echo "adb command failed"
    fi
  else
    state=$(echo $wakefulness | grep -i "asleep\|dozing")
    if [[ ! -z $state ]]
    then
      # We use this logline in espresso_helper to assert if screen is locked.
      echo "Screen is locked."
      return 200
    fi
  fi
}

rtc_app_set_use_package() {
  if [ "$1" == "v2" ]; then
    USE_RTC_APP="v2"
  else
    USE_RTC_APP="v1"
  fi
}

rtc_app_use_package() {
  local package_name="fr.pchab.AndroidRTC"
  if [ $USE_RTC_APP == "v2" ]; then
    package_name="fr.pchab.AndroidRTC2"
  fi
  echo $package_name
}

rtc_app_should_install() {
  local should_install="false"
  version=$1
  if [ ! $OS_VERSION ]; then
    get_os_version
  fi
  if [ ! $device_model ]; then
    get_device_model
  fi
  should_install_rtc_app=$(ruby $RTCAPP_RELEASE_HELPER should_install_rtc_app $version "$device_model" $OS_VERSION)
  if [ "$should_install_rtc_app" == "true" ]; then
    should_install="true"
  fi
  echo $should_install
}

check_browserstack_apps () {
  pm_list=$($ADB -s $DEVICEID shell pm list packages)
  browserstack_present=$(echo $pm_list | grep com.android.browserstack)
  webrtc_present=$(echo $pm_list | grep fr.pchab.AndroidRTC)
  if [[ -z "$browserstack_present" ]]; then
    cls_log_message "browserstack-app-not-present-on-start" "$device_model"
    $BUNDLE exec $COMMON/push_to_zombie.rb "android" "browserstack-app-not-present-on-start" "" "$device_model" "$session_id" "$DEVICEID" --is_app_accessibility "$IS_APP_ACCESSIBILITY" &
  fi
  if [[ -z "$webrtc_present" ]]; then
    cls_log_message "webrtc-app-not-present-on-start" "$device_model"
    $BUNDLE exec $COMMON/push_to_zombie.rb "android" "webrtc-app-not-present-on-start" "" "$device_model" "$session_id" "$DEVICEID" --is_app_accessibility "$IS_APP_ACCESSIBILITY" &
  fi
  if [ "$(rtc_app_should_install "v2")" == "true" ]; then
    webrtc2_present=$(echo $pm_list | grep fr.pchab.AndroidRTC2)
    if [[ -z "$webrtc2_present" ]]; then
      cls_log_message "webrtc2-app-not-present-on-start" "$device_model"
      $BUNDLE exec $COMMON/push_to_zombie.rb "android" "webrtc2-app-not-present-on-start" "" "$device_model" "$session_id" "$DEVICEID" --is_app_accessibility "$IS_APP_ACCESSIBILITY" &
    fi
  fi
}

enable_charging(){
  get_device_manufacturer
  if [[ "$manufacturer" == "samsung" ]]; then
    if device_is_a bsrun_device; then
      run_as_root "echo 0 > /sys/class/power_supply/battery/batt_slate_mode;"
    else
      # For Samsung Galaxy S22 family, don't disable protect_battery feature
      if ! device_is_a "protect_battery_enabled"; then
        $ADB -s $DEVICEID shell "settings put global protect_battery 0"
      fi
    fi
    # sometimes charging does not start, this helps to restart it
    $ADB -s $DEVICEID shell "cmd battery reset"
  fi
}

ensure_screen_on () {
  $ADB -s $DEVICEID shell "settings put global stay_on_while_plugged_in 3"
}

# call_unlock_screen function was part of driver_actions.sh
# https://browserstack.atlassian.net/browse/AUT-4080
# Reason for moving: driver_actions.sh is a very large file and while executing a function from it,
# a lot of time is wasted in reading the file. Therefore, the plan was to move out the necessary code from it.
# This change does not affect driver_actions.sh as it already imports (source) common.sh
call_unlock_screen() {
  get_os_version
  if [[ $device_model == "Redmi Note 8" ]] || [[ $device_model == 'Pixel 2' ]] || [[ $device_model == 'SM-A515F' ]]; then
      echo "CALL_UNLOCK_SCREEN: Unlock screen code begin"
      if ($ADB -s $DEVICEID shell "dumpsys power" | grep mHoldingD | grep true)
      then
          echo "CALL_UNLOCK_SCREEN: $DEVICEID screen is on, nothing to do"
      else
        if [[ $device_model == "Redmi Note 8" ]]; then
          $ADB -s $DEVICEID shell "input keyevent 26"
        else
          echo "CALL_UNLOCK_SCREEN: $DEVICEID screen is turned off, turning on"
          $ADB -s $DEVICEID shell "input keyevent KEYCODE_WAKEUP"
        fi
      fi

      if ($ADB -s $DEVICEID shell "dumpsys window" | grep "isStatusBarKeyguard" | grep true)
      then
         echo "CALL_UNLOCK_SCREEN: $DEVICEID keyguard is on, disabling"
         $ADB -s $DEVICEID shell "wm dismiss-keyguard"
      else
         echo "CALL_UNLOCK_SCREEN: $DEVICEID keyguard is dismissed, nothing to do"
      fi
  elif [[ "${OS_VERSION:0:1}" == "O" ]] || vers_gte "$OS_VERSION" "8" || [[ "$device_model" == "Pixel" ]] || [[ "$device_model" == "Pixel 3" ]]; then
    unlockoutput=$($ADB -s $DEVICEID shell "input keyevent KEYCODE_WAKEUP; wm dismiss-keyguard" 2>&1)
    if [[ ! -z  "$unlockoutput" ]]; then
      if [[ -f "$STATE_FILES_DIR/session_$DEVICEID" ]]; then
        $BUNDLE exec $ZOMBIE "android" "screen-unlock-failed" "" "$device_model" "" "$DEVICEID" ""
      else
        $BUNDLE exec $ZOMBIE "android" "screen-unlock-failed-in-cleanup" "" "$device_model" "" "$DEVICEID" ""
      fi
    fi
  else
    $ADB -s $DEVICEID shell am start com.android.browserstack/.main.UnlockScreen
    if [[ "$device_model" == "Nexus 5" ]]; then
      sleep 10
    else
      sleep 1
    fi
    $ADB -s $DEVICEID shell input keyevent 3 # home button
    if [[ "$device_model" == "SM-G950F" ]]; then
      locked=$($ADB -s $DEVICEID shell dumpsys window | grep -i mCurrentFocus | grep StatusBar) # Lock Screen
      if [[ ! -z $locked ]]; then
        $ADB -s $DEVICEID shell "wm dismiss-keyguard"
      fi
    fi
  fi
}

# check_screen function was part of driver_actions.sh
# https://browserstack.atlassian.net/browse/AUT-4080
# Reason for moving: driver_actions.sh is a very large file and while executing a function from it,
# a lot of time is wasted in reading the file. Therefore, the plan was to move out the necessary code from it.
# This change does not affect driver_actions.sh as it already imports (source) common.sh
check_screen() {
  DEVICEID="${DEVICEID:=$1}"
  $ADB -s $DEVICEID shell "settings put global stay_on_while_plugged_in 3"
  wakefulness=`$ADB -s $DEVICEID shell dumpsys power | grep 'mWakefulness'`
  called_unlock=false
  if [[ -z $wakefulness ]]; then
    lockState=`$ADB -s $DEVICEID shell dumpsys power | grep 'mWakeLockState' | sed "s/mWakeLockState=//" | tr -d " "`
    userState=`$ADB -s $DEVICEID shell dumpsys power | grep 'mUserState' | sed "s/mUserState=//" | tr -d " "`
    if [[ -z $lockState ]] && [[ -z $userState ]]; then
      echo "Waking device"
      call_unlock_screen
      called_unlock=true
    fi
  else
    state=`$ADB -s $DEVICEID shell dumpsys power | grep 'mWakefulness' | sed "s/mWakefulness=//" | tr -d " "`
    echo "State: ${state:0:6}"

    # This variable is only present in devices with NFC (near field communication), but is a reliable indicator.
    nfc_screen_state=$($ADB -s $DEVICEID shell dumpsys nfc | grep 'mScreenState=' | cut -d "=" -f2)

    if [[ ${state:0:6} == "Asleep" ]] || [[ ${state:0:6} == "Dozing" ]] || [[ $nfc_screen_state == "ON_LOCKED" ]]
    then
      echo "Waking device"
      call_unlock_screen
      called_unlock=true
    fi

    if [[ "$called_unlock" == "false" ]] && device_is_a "call_unlock_screen"; then
      call_unlock_screen
    fi

    if [[ "$device_model" == "SM-G930F" ]] || [[ "$device_model" == "LG-H850" ]] || [[ "$device_model" == "HTC 10" ]]
      then
      state=`$ADB -s $DEVICEID shell dumpsys power | grep 'mWakefulness=' | sed "s/mWakefulness=//" | tr -d " "`
      if [[ ${state:0:6} == "Asleep" || ${state:0:6} == "Dozing" ]]
      then
        echo "Dismiss black screen"
        if [[ "$device_model" == "SM-G930F" ]]
          then
          $ADB -s $DEVICEID shell input keyevent 3
        elif [[ "$device_model" == "LG-H850" ]]
          then
          $ADB -s $DEVICEID shell "input keyevent 26; sleep 1;  input keyevent 26"
        elif [[ "$device_model" == "HTC 10" ]]
          then
          $ADB -s $DEVICEID shell "input keyevent 26"
        fi
      fi
    fi
  fi

  is_unlock_screen=$($ADB -s $DEVICEID shell dumpsys window | grep 'mCurrentFocus' | grep 'UnlockScreen')

  echo "Unlock screen state: $is_unlock_screen"
  state=`$ADB -s $DEVICEID shell dumpsys power | grep 'mWakefulness' | sed "s/mWakefulness=//" | tr -d " "`
  echo "Device State: $state"

  if [[ ! -z $is_unlock_screen ]]; then
    $ADB -s $DEVICEID shell input keyevent KEYCODE_BACK
  fi


  # <MOB-3756 debug>
  # Adding this as I could not reproduce, so I needed more logging, and I may
  # as well retry the unlock screen if it failed to help the customers.
  is_the_phone_really_awake=`$ADB -s $DEVICEID shell dumpsys power | grep 'mWakefulness=' | sed "s/mWakefulness=//" | tr -d " " | grep Awake`
  is_the_keyguard_really_dismissed=`$ADB -s $DEVICEID shell "dumpsys window" | grep "isStatusBarKeyguard" | grep false`
  if [[ $device_model == "SM-G960F" ]] ; then
      if [[ -z $is_the_phone_really_awake ]] || [[ -z $is_the_keyguard_really_dismissed ]]; then
          echo "The S9 failed to wake up, retrying"
          $BUNDLE exec $ZOMBIE "android" "S9-failed-to-unlock" "$DEVICEID" "" "" "$device_model"

          # Retry
          call_unlock_screen

          # Verify
          echo "Phone is really awake? "`$ADB -s $DEVICEID shell dumpsys power | grep 'mWakefulness=' | sed "s/mWakefulness=//" | tr -d " " | grep Awake`
          echo "Keyguard really dismissed?"`$ADB -s $DEVICEID shell "dumpsys window" | grep "isStatusBarKeyguard" | grep false`
          echo "NFC screen state: "`$ADB -s $DEVICEID shell dumpsys nfc | grep 'mScreenState=' | cut -d "=" -f2`
      fi
  fi
  # </MOB-3756 debug>
}

ensure_screen_is_unlocked () {
  # set lock timeout to a huge number to avoid devices getting auto-locked after unlocking.
  $ADB -s $DEVICEID shell settings put system screen_off_timeout 86400000
  # bash $DRIVER_ACTIONS device_screen $DEVICEID
  # instead of calling above function from above script, we moved the functions here
  get_device_model
  enable_charging &
  get_session_genre
  check_screen
  # don't know why we do this, check and remove sometime
  # get_device_model # this is commented as we are calling once before it.
  ensure_screen_on
  check_screen_lock
  is_screen_locked=$?
  if [[ "$is_screen_locked" = "200" ]]; then
    cls_log_message "screen-locked-after-start" "$device_model"
    if [[ ! -z "$state" ]]; then
      $ADB -s $DEVICEID shell input keyevent 26
    fi
    # bash $DRIVER_ACTIONS device_screen $DEVICEID
    get_device_model
    get_session_genre
    check_screen
    get_rtc_session_id
    ensure_screen_on
    check_screen_lock
    is_screen_still_locked=$?
    if [[ "$is_screen_still_locked" = "200" ]]; then
      device_model=`adb_getmodel "$DEVICEID"`
      $BUNDLE exec $COMMON/push_to_zombie.rb "android" "screen-locked-after-start" "$DEVICEID" "$device_model" "$session_id"
    fi
  fi

  # set lock timeout to a huge number to avoid devices getting auto-locked after unlocking.
  $ADB -s $DEVICEID shell settings put system screen_off_timeout 86400000 &
}

unplug_battery_in_session(){
  if device_is_a "unplug_battery_in_session"; then
    $ADB -s $DEVICEID shell "cmd battery unplug"
  fi
}

ensure_home_screen_focused(){
  get_current_focus
  if [[ $current_focus =~ "launcher" ]]; then
    echo "Focus is already on home screen"
  else
    echo "Device not focussed on home screen: $current_focus"
    run_ruby_code $POPUP_HELPER "dismiss_deprecated_app_popup" "$DEVICEID" "$OS_VERSION" "$session_id"
    # Press home
    $ADB -s $DEVICEID shell input keyevent KEYCODE_HOME
  fi
}

ensure_keyboard_state(){
  get_keyboard_state
  if [[ $keyboard_state =~ "true" ]]; then
   echo "Keyboard found in current focus : $keyboard_state"
   $BUNDLE exec $ZOMBIE "android" "keyboard-focus-enabled" "" "" "" "$DEVICEID"
   $ADB -s $DEVICEID shell input keyevent KEYCODE_HOME # Press home
  else
   echo "Device not focussed on Keyboard: $keyboard_state"
  fi
}

redovpn_home_screen() {
  if [ -f $STATE_FILES_DIR/network_simulation_mid_session_$DEVICEID ]; then
    # We do not want to click on home screen in middle of a session.
    # Source of this Block: We redovpn for network simulation mid session.
    return 0
  else
    # redovpn is called from device_check too and that can leave the
    # browserstack app open, ensuring home screen is visible at the end
    ensure_home_screen_focused
  fi
}

function log() {
  local level=${1?}
  shift
  local code= line="[$(date -u '+%F %T') UTC] $DEVICEID $level: $*"
  if [ -t 2 ]
  then
     case "$level" in
     INFO) code=36 ;;
     DEBUG) code=30 ;;
     WARN) code=33 ;;
     ERROR) code=31 ;;
     *) code=37 ;;
     esac
     echo "\033[${code}m${line}\033[0m"
  else
     echo "$line"
  fi
}

function send_alert() {
  # when sending production alerts pass FYI=false
  PPL=$1
  SUB=$2
  MSG=${3:-""}
  FYI=${4:-"true"}
  curl -d fyi="$FYI" -d people="$PPL" -d subject="$SUB" -d message="$HOSTNAME: $DEVICEID : $MSG" -d mobile=true https://alert-external.browserstack.com/alert
}

set_browser_only_flag(){
  if [[ "$1" == "HTC 10" ]] || [[ "$1" == "E6653" ]]; then
    export BROWSER_ONLY_DEVICE=true
  else
    export BROWSER_ONLY_DEVICE=false
  fi
}


# Ported to APKInstaller
set_package_verifier() {
    $ADB -s $DEVICEID shell "settings put global package_verifier_enable $1; settings put global verifier_verify_adb_installs $1;"
}

set_package_verifier_user_consent() {
  # enable or disable Google Play Protect settings by setting to 1 or -1
  $ADB -s $DEVICEID shell "settings put secure package_verifier_user_consent $1; settings put secure upload_apk_enable $1; settings put global package_verifier_user_consent $1;"
}

# Ported to APKInstaller
enable_package_verifier() {
    set_package_verifier 1
}

# Ported to APKInstaller
disable_package_verifier() {
    set_package_verifier 0
}

social_media_apps(){
  DEVICEID="$1"
  switch="$2"
  fetch_props $DEVICEID

  apps_list="com.instagram.android|com.facebook.appmanager|com.whatsapp"
  packages=`$ADB -s $DEVICEID shell pm list packages -u | sed -n 's/.*://p' | grep -E $apps_list`
  get_os_version

  # pm hide-unhide supported
  if vers_gte "$OS_VERSION" "5"; then
    if [[ $switch = disable ]]; then
      switch=hide
    else
      switch=unhide
    fi
  fi

  cmd=
  for package in ${packages[@]}; do
    cmd="$cmd pm $switch ${package//$'\r'/};"
  done

  if [[ ! -z "$cmd" ]]; then
    # pm hide requires run_as_root from Android 7
    if vers_gte "$OS_VERSION" "5" && vers_lt "$OS_VERSION" "7"; then
      $ADB -s $DEVICEID shell "$cmd"
    else
      run_as_root "$cmd"
    fi
  fi
}


play_services() {
  DEVICEID="$1"
  switch="$2"
  get_device_model
  if [[ "$device_model" == "Nexus 9" ]]; then
    run_as_root "pm $switch com.google.android.gms"
  fi
}

play_store() {
  DEVICEID="$1"
  switch="$2"
  if device_is_a bsrun_device; then
      run_as_root "pm $switch com.android.vending"
      local run_as_root_return_code=$?
      if [[ "$run_as_root_return_code" != 0 ]]; then
        record_cleanup_failure_reason "Unable to enable play store: $run_as_root_return_code"
        exit 200
      fi
  else
      play_store_no_root $DEVICEID $switch
  fi
}

play_store_no_root() {
  DEVICEID="$1"
  switch="$2"
  if [[ $switch == enable ]]; then
      $ADB -s $DEVICEID shell pm enable com.android.vending
  else
      $ADB -s $DEVICEID shell pm disable-user com.android.vending
  fi
}

restart_check_status_bar(){
  local product=$1
  local contacts_access_param=$2
  local google_account_sign_in_param=$3
  STATUSBAR_PID_FILE="$DATA_LOCAL_TMP/check_statusbar_pid"
  $ADB -s $DEVICEID shell <<__EOF &
kill \$(cat $STATUSBAR_PID_FILE)
rm $STATUSBAR_PID_FILE
sh $DATA_LOCAL_TMP/check_status_bar_$product.sh "$contacts_access_param" "$google_account_sign_in_param" &
exit
__EOF
}

get_rtc_session_id() {
  session_id=$(cat $CONFIG/rtc_service_$DEVICEID | jq ".live_session_id" | tr -d "\r\n\"")
  if [ -z $session_id ] || [[ $session_id = "null" ]]; then
    session_id=$(cat $CONFIG/rtc_service_$DEVICEID | jq ".app_live_session_id" | tr -d "\r\n\"")
  fi
  if [ -z $session_id ] || [[ $session_id = "null" ]]; then
    session_id=$(cat $CONFIG/rtc_service_$DEVICEID | jq ".automate_session_id" | tr -d "\r\n\"")
  fi
}

fetch_last_session_info() {
  session_info=`cat /tmp/duplicate_session_$DEVICEID`
  genre=$(echo "$session_info" | jq ".genre" | tr -d "\r\n\"")
  last_session_id=$(echo "$session_info" | jq ".live_session_id" | tr -d "\r\n\"")
  last_session_type="live"
  if [ -z $last_session_id ] || [[ $last_session_id = "null" ]]; then
    last_session_id=$(echo "$session_info" | jq ".app_live_session_id" | tr -d "\r\n\"")
    last_session_type="app_live"
  fi
  if [ -z $last_session_id ] || [[ $last_session_id = "null" ]]; then
    last_session_id=$(echo "$session_info" | jq ".session_id" | tr -d "\r\n\"")
    last_session_type="automate"

    if [[ "$genre" == "app_automate" ]]; then
      last_session_type="app_automate"
    fi
    if [[ $genre == "speedlab" ]]; then
      last_session_type="speedlab"
    fi
  fi

  # WARN: this string is being parsed from cleanup.rb
  echo "Last Session Info: ID: $last_session_id, Type: $last_session_type"
}

log_gms_version() {
  gms_version=`$ADB -s $DEVICEID shell "dumpsys package com.google.android.gms | grep versionName" | head -1 | cut -f2 -d'=' | cut -f1 -d' '`
  if [ -z $session_id ] || [[ $session_id = "null" ]]; then
    get_rtc_session_id
  fi
  $COMMON/push_to_cls.rb $CONFIG/rtc_service_$DEVICEID "gms_version" "$gms_version" device_id:$DEVICEID &
  PUSH_TO_CLS_PID="$!"
  $BUNDLE exec $COMMON/push_to_zombie.rb "android" "gms_version" "$gms_version" "" "" "$DEVICEID" "$session_id" --is_app_accessibility "$IS_APP_ACCESSIBILITY" &
  PUSH_TO_ZOMBIE_PID="$!"
  wait "$PUSH_TO_CLS_PID" "$PUSH_TO_ZOMBIE_PID"
}

dismiss_crash_popup() {
  sleep 1
  run_ruby_code $POPUP_HELPER "dismiss_crash_popup" "$DEVICEID" "$OS_VERSION" "$session_id"
}

log_app_anr() {
  sleep 1
  keyguard_status=`$ADB -s $DEVICEID shell "dumpsys window" | grep "isStatusBarKeyguard"`
  current_window=`$ADB -s $DEVICEID shell "dumpsys window | grep mCurrent"`
  anr_detected=`echo "$current_window" | grep 'Application Not Responding'`
  if [[ ! -z "$anr_detected" ]]; then
    run_ruby_code $UI_AUTOMATION_HELPER "handle_click_anr_popup" "$DEVICEID" "$OS_VERSION" "$session_id"

    get_rtc_session_id

    vpn_anr_detected=`echo "$current_window" | grep 'Application Not Responding: com.google.android.vpntether'`
    if [[ ! -z "$vpn_anr_detected" ]]; then
      $ADB -s $DEVICEID shell am force-stop com.google.android.vpntether
      $BUNDLE exec $COMMON/push_to_zombie.rb "android" "anr_usbvpn" "" "" "" "$DEVICEID" "$session_id" --is_app_accessibility "$IS_APP_ACCESSIBILITY" &
    else
      $BUNDLE exec $COMMON/push_to_zombie.rb "android" "anr_user_application" "" "" "" "$DEVICEID" "$session_id" --is_app_accessibility "$IS_APP_ACCESSIBILITY" &
    fi

  fi
}

disable_chrome_welcome_screen() {
  $ADB -s $DEVICEID shell am set-debug-app --persistent com.android.chrome
  $ADB -s $DEVICEID shell "echo 'chrome --disable-fre' > $DATA_LOCAL_TMP/chrome-command-line"
  run_ruby_code $CHROME_FLAGS_HELPER "$DEVICEID" "ignore_mitm_cert_errors"
}

get_device_manufacturer() {
  manufacturer=`adb_getprop $DEVICEID "ro.product.manufacturer"`
}

get_device_type() {
  device_type=`adb_getprop $DEVICEID "ro.build.characteristics"`
}

get_current_focus() {
  current_focus=`$ADB -s $DEVICEID shell "dumpsys window | grep mCurrentFocus"`
}

get_keyboard_state(){
  keyboard_state=`$ADB -s $DEVICEID shell "dumpsys input_method | grep mInputShown"`
}

get_setting() {
  namespace=$1
  key=$2
  setting=`$ADB -s $DEVICEID shell settings get $namespace $key`
}

run_popup_handler() {
  run_ruby_code $POPUP_HELPER "run_main_automation" "$DEVICEID" "$OS_VERSION" "$session_id"
}

run_ui_automation_check_package_installed() {
  ConsentDialogActivityName="com.google.android.vending.verifier.ConsentDialog|com.google.android.finsky.verifier.impl.ConsentDialog|com.oneplus.setupwizard|com.google.android.permissioncontroller"

  is_app_installed="false"
  check_package_name=$1
  num_retries=$2

  apk_installed=`$ADB -s $DEVICEID shell pm list packages | grep package:$check_package_name`
  if [ ! -z "$apk_installed" ]; then
    echo "App installed without handling consent"
    return 0
  fi

  RETRY_COUNT=0
  user_consent_happened=false

  while [ $RETRY_COUNT -le $num_retries ];
  do
    consentwindowoutput=`$ADB -s $DEVICEID shell dumpsys window | grep -E 'mCurrentFocus|mFocusedApp' | grep -E $ConsentDialogActivityName`
    if [ "$user_consent_happened" == "false" ] || [ ! -z "$consentwindowoutput" ]; then
      if vers_gte "$OS_VERSION" "11"; then
        $ADB -s $DEVICEID shell "am instrument -w -r -e debug false -e class 'com.browserstack.uiautomation.PopUpHandlerTest#testHandlePopup' com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner"
      else
        $ADB -s $DEVICEID shell uiautomator runtest PopupHandlerAutomation.jar -c com.browserstack.popupHandler.MainAutomation#testHandlePopup
      fi
      user_consent_happened=true
    fi

    apk_installed=`$ADB -s $DEVICEID shell pm list packages | grep package:$check_package_name`
    if [ ! -z "$apk_installed" ]; then
      echo "App installed after $i retries"
      is_app_installed="true"
      return 0
    fi

    sleep 2
    RETRY_COUNT=$[$RETRY_COUNT+1]
  done
  if [ "$is_app_installed" == "false" ]; then
    return 1
  fi
}

log_cleanup_flow() {
    if [[ -z $DEVICEID ]]; then
        echo "No DEVICEID defined"
        return
    fi

    if [[ -z $1 ]]; then
        echo "No msg given, aborting.\n\n    Usage: log_cleanup_flow MSG"
        return
    fi

    # For the file. Fun fact: echo $1 and echo "${1}" are not the same, one
    # ignores the whitespace at the beginning.
    elapsed_seconds=`expr $(date +%s) - $cleanup_timer_start`
    cleanup_time_breakdown+="$1:$elapsed_seconds;"
    printf "(t + %3ds) $1\n" $elapsed_seconds >> /var/log/browserstack/cleanup_flow_${DEVICEID}.log

    # For the 'logs'
    echo "Cleanup flow for ${DEVICEID}: ${1}"
}

# Takes one argument: REBOOT_REASON
reboot_device() {
  REBOOT_REASON=${1:-"unknown"}
  run_ruby_code "$ANDROID_DEVICE" "$DEVICEID" "driver_actions" "reboot" "$REBOOT_REASON"
}

needs_to_run_interactions_server_with_bsrun() {
    # Some phones need interaction_server to be run not only as root, but with
    # bsrun. This is due to problems accessing the files under /dev/input.
    #
    # This needs $DEVICEID to be defined.
    #
    # Returns 0 when it needs to, so it can be used in an if else as
    #
    #     if needs_to_run_interactions_server_with_bsrun; then
    #         do_this
    #     else
    #         do_that
    #     fi
    if [ -z $device_model ]; then
        get_device_model
    fi

    if [[ $device_model == "SM-T875" ]] || [[ $device_model == "SM-T870" ]] || [[ $device_model == "SM-T876B" ]] || [[ $device_model == "SM-A115M" ]] || [[ $device_model == "SM-G981B" ]] || [[ $device_model == "SM-G981F" ]] || [[ $device_model == "SM-G986B" ]] || [[ $device_model == "SM-G988B" ]] || [[ $device_model == "SM-A515F" ]] || [[ $device_model == "SM-N986B" ]] || [[ $device_model == "SM-N981B" ]] || [[ $device_model == "SM-N980F" ]]
    then
        echo "model $device_model runs interaction server as root"
        return 0
    else
        echo "model $device_model runs interaction server as shell user"
        return 1
    fi
}

start_logcat_capture() {
  local logcat_file=/var/log/browserstack/logcat_"$DEVICEID".log

  if [ -z "`ps -ef | grep $DEVICEID | grep logcat`" ]
  then
    echo "Starting $DEVICEID logcat capture for session $session_id" >> $logcat_file
    $ADB -s $DEVICEID logcat -c
    echo "Flushed $DEVICEID logcat before for session $session_id" >> $logcat_file
    $ADB -s $DEVICEID logcat -v time | grep -i "tombstone\|fatal\|error\|debug\| e/\|vending\|gms\|browserstack\|rtc\|interactions_server\|bs_screencap\|inputinjector\|RestrictionsManager:\|usb\|adb\|orientation\|rotation\|landscape\|portrait" >> $logcat_file &
  fi
}

# Required args (All are mandatory), Refer the script for more details.
# component, subcomponent, device_id, type, is_error
push_to_influxdb_v1() {
  $BUNDLE exec ruby $PUSH_TO_INFLUXDB_SCRIPT "$@"
}

# $DEVICEID $last_session_id
cleanup_mock_camera_image() {
  $BUNDLE exec ruby $CLEANUP_MOCK_CAMERA_IMAGE "$@"
}

# <SCRIPT>, <ARGS>
# Sets the global variable `exit_msg` which can be used to see the response of the scripts (Depends on ExitFile usage in ruby)
# please put optional arguments at the end of params or directly use $BUNDLE exec ruby <ruby_script> <args> as this function will not preserve empty params
function run_ruby_code() {
  local start_time=$(date +%s)
  local bash_identifier=$(ruby $BS_DIR/mobile/android/exit_file.rb gen_id)
  local ruby_script=$1
  local ruby_function=$2
  local script_args="${@:2}"

  output=$(bash_identifier=$bash_identifier cleanup_timer_start=$cleanup_timer_start $BUNDLE exec ruby $ruby_script $script_args 2>&1)
  local exit_code=$?

  set +x # disabling logger because ruby logs will appear twice due to $output below
  bundle_error=$(echo "$output" | grep -e "[bundle install]*\(Bundler::\)\|cannot load such file")
  set -x

  if [[ -n "$bundle_error" ]]; then
    record_cleanup_failure_reason "Bundler / Gem Issue while running Ruby from Bash"
    exit 200
  fi

  #Read exit msg from the ExitFile, and set the global variable
  exit_msg="unknown" # global var
  exit_msg=$(bash_identifier=$bash_identifier ruby $BS_DIR/mobile/android/exit_file.rb "read")
  if [[ -z $exit_msg ]]; then
    echo "Unable to get the exit file, you can log the message by using ExitFile.write in ruby code"
  fi

  # Delete the status file
  bash_identifier=$bash_identifier ruby android/exit_file.rb "clear"

  time_taken=`expr $(date +%s) - $start_time`
  if [[ "$COMPONENT" == "cleanup" ]] && [[ "$time_taken" -gt 0 ]]; then
    if [[ "$ruby_function" == "push_cleanup_stats_to_zombie" ]]; then
      # Not logging this function to the cleanup stats file
      #  This function deletes the cleanup stats file when pushing the stats to zombie,
      #  when logging this function data the new file gets created
      #  and this data will then be added to the next sessions data
      echo "$(basename $ruby_script .rb)","$time_taken"
    else
      echo "$(basename $ruby_script .rb)","$time_taken" >> $CLEANUP_STATS_FILE # Write filename and time taken to cleanup stats file, e.g. "my_script,3"
    fi
  fi

  return $exit_code
}

function install_via_ruby(){
  local ruby_manager_file=$1
  local reason=$2
  local exit_on_failure="${3:-true}" # exit the process for non-zero exit code
  local from_cleanup="${4:-true}" # exit the process for non-zero exit code
  echo "install_via_ruby ${ruby_manager_file} ${reason} ${exit_on_failure} ${from_cleanup}"
  run_ruby_code $ruby_manager_file $DEVICEID "ensure_install"
  local app_installation_return_code=$?

  if [[ $app_installation_return_code -eq 0 ]]; then
    return 0
  fi

  if [[ "$from_cleanup" == "true" ]]; then
    record_cleanup_failure_reason "Unable to install ${ruby_manager_file##*/}($reason), reason: $exit_msg"
  fi

  if [[ "$exit_on_failure" == "true" ]]; then
    exit 200
  else
    return 1 # failure
  fi
}

function update_samsung_browser_preferences(){
  local reason=$1
  local exit_on_failure="${2:-true}" # exit the process for non-zero exit code
  local from_cleanup="${3:-true}" # exit the process for non-zero exit code

  run_ruby_code "$SAMSUNG_BROWSER_MANAGER" "$DEVICEID" "update_samsung_browser_preferences"
  local update_sbrowser_prefs_return_code=$?

  if [[ $update_sbrowser_prefs_return_code -eq 0 ]]; then
    return 0
  fi

  if [[ "$from_cleanup" == "true" ]]; then
    record_cleanup_failure_reason "Unable to update Samsung Browser preferences ($reason), reason: $exit_msg"
  fi

  if [[ "$exit_on_failure" == "true" ]]; then
    exit 200
  else
    return 1 # failure
  fi
}

function media_cleanup() {
  local exit_on_failure="${1:-true}" # exit the process for non-zero exit code
  local from_cleanup="${2:-true}" # exit the process for non-zero exit code

  run_ruby_code $MEDIA_MANAGER $DEVICEID
  local media_cleanup_return_code=$?
  if [[ $media_cleanup_return_code -eq 0 ]]; then
    return 0
  fi

  if [[ "$from_cleanup" == "true" ]]; then
    record_cleanup_failure_reason "Unable to clean media, reason: $exit_msg"
    retry_cleanup "unable_to_clean_media"
  fi

  if [[ "$exit_on_failure" == "true" ]]; then
    exit 200
  else
    return 1 # failure
  fi
}

screenrecord_command_on_device() {
  if device_is_a uses_alternative_screenrecord; then
    screenrecord_binary=$ALT_SCREENRECORD
  else
    screenrecord_binary='screenrecord'
  fi
  echo "$screenrecord_binary"
}

screenrecord_binary_name_on_machine() {
  get_device_model
  get_os_version
  if device_is_a uses_alternative_screenrecord; then
    screenrecord_to_install="screenrecord_$OS_VERSION"
  else
    screenrecord_to_install=''
  fi
  echo "$screenrecord_to_install"
}

get_google_accounts() {
  # Some device models require a different dumpsys command to find google accounts logged in on the device
  local accounts=`$ADB -s $DEVICEID shell "dumpsys account; dumpsys content" | grep -w "Account" | grep -i "google"`
  echo "$accounts"
}

# Need to clean this up. We can make these apks' path consistent across different appium versions.
# Relevant Task: https://browserstack.atlassian.net/browse/AA-3947
get_appium_apks_path() {
  APPIUM_VERSION=$1
  AUTOMATION_NAME=$2
  AUTOMATION_VERSION=$3
  APPIUM_ROOT="/usr/local/.browserstack/appium_"$APPIUM_VERSION"_bstack"
  UIAUTOMATOR2_SERVER_TEST_APK="appium-uiautomator2-server-debug-androidTest.apk"
  APPIUM_UNICODE_IME_PATH="node_modules/appium-android-ime/bin/UnicodeIME-debug.apk"
  APPIUM_UNLOCK_PATH="node_modules/appium-unlock/bin/unlock_apk-debug.apk"
  UIAUTOMATOR2_APKS_PATH="node_modules/appium-uiautomator2-server/apks"
  UIAUTOMATOR2_SERVER_APK="appium-uiautomator2-server.apk"
  APPIUM_SETTINGS_PATH="node_modules/io.appium.settings/apks/settings_apk-debug.apk"

  if vers_gte "$APPIUM_VERSION" "2.0.0"; then
    if [ "$AUTOMATION_NAME" = "flutterintegration" ]; then
      APPIUM_ROOT="/usr/local/.browserstack/appium_"$APPIUM_VERSION"_bstack/packages/appium/$AUTOMATION_NAME/$AUTOMATION_VERSION/node_modules/appium-flutter-integration-driver"
      UIAUTOMATOR2_APKS_PATH="node_modules/appium-uiautomator2-driver/node_modules/appium-uiautomator2-server/apks"
      APPIUM_SETTINGS_PATH="node_modules/appium-uiautomator2-driver/node_modules/io.appium.settings/apks/settings_apk-debug.apk"
    else
      APPIUM_ROOT="/usr/local/.browserstack/appium_"$APPIUM_VERSION"_bstack/packages/appium/$AUTOMATION_NAME/$AUTOMATION_VERSION/node_modules/appium-$AUTOMATION_NAME-driver"
    fi
  fi
}

instrument_device_off_adb(){
  local source=${1:-"espresso_actions"}
  local output=${2}
  local status=${3}
  local adb_issue_regex="device .* not found|device offline|device unauthorized"

  if [[ $output =~ $adb_issue_regex ]]; then
    local session_id=`mobile_session_get "session_id" | tr -d '"'`
    local data="{\"status\": $status, \"source\": $source}"
    $BUNDLE exec $ZOMBIE "android" "device-off-adb" "$output" "" "$data" "$DEVICEID" "$session_id"
  fi
}

safe_adb() {
  local timeout="$1"
  local cmd="adb ${@:2}"

  if [[ -n "$timeout" ]]; then
      cmd="timeout $timeout adb ${@:2}"
  fi

  local adb_output=$($cmd 2>&1)
  local status=$?

  instrument_device_off_adb "${FUNCNAME[1]}" "$adb_output" "$status"
  echo "$status"
}

instrument_copy_error(){
  local source=${1:-"espresso_actions"}
  local output=${2}
  local status=${3}
  local cp_issue_regex="No space left on device"

  if [[ $output =~ $cp_issue_regex ]]
  then
    local session_id=`mobile_session_get "session_id" | tr -d '"'`
    local data="{\"status\": $status, \"source\": $source}"
    echo "$session_id Detected espresso system copy error : $data"
    $BUNDLE exec $ZOMBIE "android" "copy-error" "$output" "" "$data" "$DEVICEID" "$session_id"
    return 1
  fi
  return 0
}

safe_cp() {
  local cp_cmd="cp $1 $2"

  cp_output=$($cp_cmd 2>&1)
  local status=$?

  instrument_copy_error "${FUNCNAME[1]}" "$cp_output" "$status"
  local copy_error_found=$?

  echo "$status"
  return $copy_error_found
}

# Decorator function to record time taken by cleanup shell functions and record their time in a file.
function record_time() {
  eval "
    _inner_$(typeset -f "$1")
    $1"'() {
      start_time=`expr $(date +%s)`
      _inner_'"$1"' "$@"
      local ret=$?
      time_diff=`expr $(date +%s) - $start_time`

      echo '"$1"',"$time_diff" >> "$CLEANUP_STATS_FILE"

      return "$ret"
    }'
}

function is_dedicated_cleanup() {
  [[ ! -f "$STATE_FILES_DIR/dedicated_cleanup_$DEVICEID" ]]
  IS_DEDICATED_CLEANUP=$?
  if [[ $IS_DEDICATED_CLEANUP -eq 1 ]]; then
    DEDICATED_CLEANUP_CONFIG_STR=`cat "$STATE_FILES_DIR/dedicated_cleanup_$DEVICEID"`
    IFS=',' DEDICATED_CLEANUP_CONFIG=(${DEDICATED_CLEANUP_CONFIG_STR})
  fi
}

function is_dedicated_minified_cleanup() {
  [[ ! -f "$STATE_FILES_DIR/dedicated_minimized_cleanup_reserved_$DEVICEID" ]]
  IS_DEDICATED_MINIFIED_CLEANUP=$?
}

function should_clean_for_dedicated() {
  local command_name=$1
  local should_clean=0
  if [[ ! $IS_DEDICATED_CLEANUP -eq 1 ]]; then
    echo 0;
    return;
  fi
  for command in "${DEDICATED_CLEANUP_CONFIG[@]}"
  do
    if [[ "$command" = "$command_name" ]]; then
      should_clean=1
      break
    fi
  done
  echo $should_clean;
  return;
}
