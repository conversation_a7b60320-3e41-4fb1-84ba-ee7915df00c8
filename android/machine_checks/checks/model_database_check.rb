require_relative '../machine_check_config'

class ModelDatabaseCheck
  include MachineCheckConfig

  attr_reader :errors

  def initialize
    @model_database_file = config['model_database_file']
    @errors = []
  end

  def perform
    validations
  end

  def validations
    @errors << "Model database file not found, needs deploy to setup db" unless model_db_file_present?
  end

  def model_db_file_present?
    File.exist?(@model_database_file)
  end
end
