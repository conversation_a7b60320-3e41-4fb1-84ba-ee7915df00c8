mobile_home: "/usr/local/.browserstack/mobile"

machine_env_file: '/usr/local/.browserstack/env'
static_conf_file: '/usr/local/.browserstack/config/static_conf.json'
install_script: '/usr/local/.browserstack/mobile/android/machine_checks/machine_check_install.sh'
model_database_file: '/usr/local/.browserstack/mobile_database.sqlite'
chrome_har_capturer_server_file: '/usr/local/.browserstack/chrome_har_capturer_server'
chrome_har_capturer_server_service: '/service/chrome_har_capturer_server/run'

deps_chrome_dir: '/usr/local/.browserstack/deps/chrome'

alert_url: 'https://alert-external.browserstack.com/alert'
alert_to: 'mobile'

adb_version: '1.0.41'
privoxy_version: '3.0.29'
node_version: 'v5.6.0'
nvm_version: 'v0.34.0'
nvm_node_versions:
  - 'v8.6.0'
  - 'v12.6.0'

device_logger_service_file: '/service/device_logger/run'
async_network_logs_processer_service_file: '/service/async_network_logs_processer/run'
async_other_logs_processer_service_file: '/service/async_other_logs_processer/run'
async_video_logs_processer_service_file: '/service/async_video_logs_processer/run'

download_endpoint:
  eu-west-1: '**************'
  eu-west-2: '**************'
  eu-central-1: '*************'
  us-west-1: '************'
  us-east-1: '**************'
  ap-southeast-2: '**************'
  ap-south-1: '************'

state_files_dir: '/usr/local/.browserstack/state_files'
username: 'ritesharora'

machine_inventory_ip: 'https://mobile-inventory.bsstag.com'
machine_inventory_port: 443

checkers_list:
  - 'reboot_check'
  - 'abrt_java_connector_removal_check'
  - 'adb_check'
  - 'create_device_logger_services'
  - 'add_machine_to_ansible_inventory'
  - 'android_chrome_har_capturer_deploy_check'
  - 'android_mitmproxy_deploy_check'
  - 'async_network_logs_processer_setup_check'
  - 'async_other_logs_processer_setup_check'
  - 'async_video_logs_processer_setup_check'
  - 'bundler_check'
  - 'check_android_build_tools'
  - 'check_device_logger_stale'
  - 'check_jacoco_cli_md5'
  - 'check_java_version'
  - 'clean_orphan_puma_workers'
  - 'consul_check'
  - 'date_check'
  - 'device_logger_setup_check'
  - 'ensure_curl'
  - 'ensure_ip'
  - 'ensure_nomad_running'
  - 'ensure_pointed_to_prod'
  - 'ensure_ssh_password_login_disabled'
  - 'ensure_usb_controller_up'
  - 'ensure_wifi_disabled'
  - 'firewall_check'
  - 'imagemagick_setup_check'
  - 'kernel_module_install_check'
  - 'logrotate_config_check'
  - 'model_database_check'
  - 'mp4box_setup_check'
  - 'remove_tmp_files'
  - 'stale_cleanup_processes_check'
  - 'state_files_dir_check'
  - 'static_conf_check'
  - 'timezone_check'
  - 'truncate_adb_log'
  - 'udp_server_check'
  - 'user_access_grant_check'
  - 'zipalign_tool_check'
  - 'close_wait_check'
  - 'kill_stale_chromedrivers'
  - 'host_stats'
  - 'samsung_download_mode_check'
  - 'update_privoxy_domain_blocking'
  - 'privoxy_block_whitelist_domain'
  - 'download_app_bundleid_block_config'
  - 'restart_puma_server'
  - 'oom_killer_check'
  - 'create_privoxy_systemd_services'
  - 'create_adb_forwarder_systemd_services'

# SSH key that enigma uses to automate access grants.
access_grant_ssh_key: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC4H6DgVblPnAYT77nCsEYkPwjOvDlodLvQ+ow2nzKOygXqFkXPoIpasi7+pZVvJ7e4oc9jJBSgHY/nsExESdiZ8PJQ2IcpjN1HCfsn457wwJ19xdJcDn6IG6Wo9ODaUt6ICTInQWr0t4BvswjCGgVp6mkg5z9iN2RSQ467G69cEAh8l2XZSydqStZquCKmEZP8JbjuWtl+6A0o+8YEUMRuX3Kob8weW4ViMuDWEsNbRjQICuLLtLdjhDaea+N7CdGVLDQwKBw5NYjWXx6j9P07QuTRSQgzgTdvskFmySNkrAHPUw9+3CLZwmiJ9/Bh4Pd2AWvmMMmXSOH5lI2xWPzX <EMAIL>"

java_version: "8"

android_build_tools_versions:
  - '29.0.2'

android_sdk_home: "/usr/local/.browserstack/android-sdk/"

jacococli_jar_md5: "3873683f8a013c679ca13ef648a516e4"

zipalign_tool_path: "/usr/local/.browserstack/android-sdk/build-tools/29.0.2/zipalign"
zipalign_tool_md5sum: "c199875ad0230e36b60ef0ee3917e278"

