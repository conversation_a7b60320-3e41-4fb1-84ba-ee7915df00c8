require 'sequel'
require_relative '../constants'

module BrowserStack
  # Simple wrapper over Sequel to interact with our database
  class Database
    def self.connect
      @@db ||= Sequel.connect("sqlite://#{DATABASE_PATH}")
      @@db
    end
  end

  class AppsDatabase
    def self.all_apps
      # Return array of all known apps installed on BrowserStack devices
      db = BrowserStack::Database.connect
      all_apps_dataset = db[:apps] # SQLite dataset
      all_apps_dataset.to_a.map { |app| app[:name] }

    end
  end

  class CleanupRules
    def self.cleanup_rules
      db = BrowserStack::Database.connect
      db[:cleanup_rules] # SQLite dataset
    end
  end
end
