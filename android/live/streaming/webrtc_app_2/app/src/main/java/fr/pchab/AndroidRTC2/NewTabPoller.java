package fr.pchab.AndroidRTC2;

import org.json.JSONObject;
import org.json.JSONArray;
import org.json.JSONException;
import org.webrtc.DataChannel;

import android.util.Log;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.Exception;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSession;

public class NewTabPoller implements Runnable {

    private static final String TAG = NewTabPoller.class.toString();
    private static final int INTERVAL = 3000;
    private static final int FAILURE_INTERVAL = 50;
    private static JSONArray pageInfo;
    private static boolean multiple_tabs = false;
    private static boolean multiple_tabs_opened = false;
    private String debugger_url;
    private WebRtcClient wClient;
    HostnameVerifier hostnameVerifier = new HostnameVerifier() {
                                            @Override
                                            public boolean verify(String hostname, SSLSession session) {
                                                return true;
                                            }
                                        };

    public NewTabPoller(WebRtcClient wClient, String debugger_url){
        this.wClient = wClient;
        this.debugger_url = debugger_url;
    }

    public NewTabPoller(){
    }

    private String getString(BufferedReader reader) throws IOException{
        StringBuilder sb = new StringBuilder();
        String line = null;
        while ((line = reader.readLine()) != null) {
            sb.append(line).append("\n");
        }
        reader.close();
        Log.i(TAG, "string from BufferedReader: " + sb.toString());
        return sb.toString();
    }

    public String[] getActivePageInfo(URL url) throws Exception {
        InputStream inStream = null;
        try {
            HttpsURLConnection urlConnection = (HttpsURLConnection)url.openConnection();
            urlConnection.setRequestProperty("Content-Type", "application/json");
            urlConnection.setHostnameVerifier(hostnameVerifier);
            urlConnection.setConnectTimeout(1000);
            urlConnection.setReadTimeout(1000);
            inStream = urlConnection.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inStream));
            String json = getString(reader);
            String pageIDandURL[] = new String[2];
            pageInfo = new JSONArray(json);
            if(pageInfo.length() == 0){
                Log.i(TAG,"No page open");
                pageIDandURL[0]="noPage";
                pageIDandURL[1]="noPage";
                return pageIDandURL;
            }
            JSONArray typePageObject = new JSONArray();
            for (int i = 0; i < pageInfo.length(); i++) {
                JSONObject tab = pageInfo.getJSONObject(i);
                if ("page".equals(tab.optString("type"))) {
                    typePageObject.put(tab);
                }
            }
            JSONObject currTab = typePageObject.getJSONObject(0);
            String pageID = currTab.getString("id");
            String pageUrl = currTab.getString("url");
            if (inStream != null) {
                inStream.close();
            }
            pageIDandURL[0]= pageID;
            pageIDandURL[1] = pageUrl;
            return pageIDandURL;
        } catch (Exception e) {
            if (inStream != null) {
                inStream.close();
            }
            throw e;
        }
    }

    public void checkTabClosed(String prevPageID,int numTabs) throws Exception{
        boolean tab_closed = false;
        if (prevPageID != "tmpPage"){
            tab_closed = true;
            for (int i=0; i < numTabs; i++){
                JSONObject currTab = pageInfo.getJSONObject(i);
                if (prevPageID.equals(currTab.getString("id"))){
                    tab_closed = false;
                    break;
                }
            }
        }
        if (tab_closed && !prevPageID.equals("noPage")) {
            Log.i(TAG, "Tab " + prevPageID + " closed");
            JSONObject jsonData = new JSONObject();
            jsonData.put("tab closed: ", prevPageID);
            RTCSessionUtils.logData("tab_closed", null, jsonData , null);
            wClient.sendMessage(prevPageID,"tab_closed","screenPeer");
        }
    }

    public boolean checkMultipleTabs(int numTabs) throws Exception{
        if (!multiple_tabs){
            if (numTabs < 2){
                return false;
            }
            else if (numTabs > 2  || ( !pageInfo.getJSONObject(0).getString("url").equals("about:blank") && !pageInfo.getJSONObject(1).getString("url").equals("about:blank"))){
                multiple_tabs = true;
            }
            if (multiple_tabs){
                JSONObject jsonData = new JSONObject();
                jsonData.put("tab count", numTabs);
                RTCSessionUtils.logData("multiple_tabs_opened", null, jsonData , null);
                if (multiple_tabs_opened == false) {
                    JSONObject live_session_report_data = new JSONObject();
                    try {
                        live_session_report_data.put("multiple_tabs_opened", "true");
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    RTCSessionUtils.updateLiveSessionReport(live_session_report_data);
                    multiple_tabs_opened = true;
                }
            }
        }
        return multiple_tabs;
    }


    public boolean recordTabActivity(String prevPageID) throws Exception{
        int numTabs = pageInfo.length();
        Log.i(TAG, "Number of tabs open: " + numTabs );
        checkTabClosed(prevPageID,numTabs);
        return checkMultipleTabs(numTabs);
    }

    public void run() {
        try {
            String prevPageID = "tmpPage";

            URL url = new URL(debugger_url);
            Log.i(TAG,"URL: " + debugger_url);
            while(true){
                try {
                    Log.i(TAG,"URL: " + debugger_url);
                    String pageIDandURL[] = getActivePageInfo(url);
                    String pageID = pageIDandURL[0];
                    String pageUrl = pageIDandURL[1];
                    if (pageUrl.startsWith("chrome://") == true) {
                        continue;
                    }
                    if(!prevPageID.equals(pageID)){
                        if(pageInfo.length()!=0){
                            wClient.sendMessage(pageID,"newTab","screenPeer");
                        }
                        recordTabActivity(prevPageID);
                        prevPageID = pageID;
                    }
                    Thread.sleep(INTERVAL);
                } catch (Exception e) {
                    Log.e(TAG, "error sending newtab event : " + e.getMessage());
                    Thread.sleep(FAILURE_INTERVAL);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "error initializing newtab poll thread", e);
        }
    }
}
