<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          package="fr.pchab.AndroidRTC2"
          android:versionCode="1"
          android:versionName="2.7">

    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>

    <application android:label="@string/app_name" android:icon="@drawable/icon">
        <service android:name="RTCService" android:exported="true">
        </service>
        <activity
            android:name=".ProjectionActivity"
            />
        <service android:name=".MediaRecordService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection"
            />

        <service
            android:name=".BSAccessibilityService"
            android:enabled="true"
            android:label="@string/accessibility_service_label"
            android:description="@string/accessibility_service_description"

            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:exported="true">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <service android:name=".BrowserStackIME"
            android:enabled="true"
            android:label="@string/keyboard_service_label"
            android:description="@string/keyboard_service_description"

            android:permission="android.permission.BIND_INPUT_METHOD"
            android:exported="true">
            <intent-filter>
                <action android:name="android.view.InputMethod" />
            </intent-filter>
            <meta-data
                android:name="android.view.im"
                android:resource="@xml/input_method_config" />
        </service>
    </application>
</manifest>
