package com.browserstack.uiautomation;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.uiautomator.UiObject;
import androidx.test.uiautomator.UiSelector;
import androidx.test.uiautomator.UiObjectNotFoundException;
import androidx.test.uiautomator.UiScrollable;
import org.junit.Test;
import org.junit.runner.RunWith;
import static java.lang.Thread.sleep;

import android.os.Build;

import com.browserstack.uiautomation.android.Android;
import com.browserstack.uiautomation.android.ManufacturerFactory;


@RunWith(AndroidJUnit4.class)
public class DisableMacRandomizationTest extends CommonTest {
    private final String TEXT = "text";
    private final String ID = "id";
    private final String CLASS_NAME = "className";
    private final String DESCRIPTION = "description";

    @Test
    public void testDisableMacRandomization() throws Exception {

        if (Build.VERSION.SDK_INT < 35){
            startActivity("com.android.settings");

            UiObject connectionSettings = findElement("Connections", 10, TEXT);
            clickElement(connectionSettings, "connectionSettings");

            UiObject wifiSettings = findElement("Wi-Fi", 10, TEXT);
            clickElement(wifiSettings, "wifiSettings");
        }

        UiObject wifiDetails = findElement("com.android.settings:id/wifi_details", 10, ID);
        clickElement(wifiDetails, "wifiDetails");

        UiObject viewMore = findElement("View more", 10, TEXT);
        clickElement(viewMore, "viewMore");

        UiScrollable appViews = new UiScrollable(new UiSelector().scrollable(true));
        appViews.scrollTextIntoView("MAC address type");

        sleep(3000); //add sleep to allow for text to be found, removing causes flakiness

        UiObject addressType = findElement("MAC address type", 10, TEXT);
        clickElement(addressType, "addressType");

        UiObject phoneMacType = findElement("Phone MAC", 10, TEXT);
        clickElement(phoneMacType, "phoneMacType");

    }

    private UiObject findElement(String identifier, int timeout, String findBy) throws InterruptedException {
        UiObject element = null;
        for (int i = 0; i < timeout; i++) {
            log("Finding element " + identifier + " i: " + i);
            if (findBy.equals(TEXT)) {
                element = new UiObject(new UiSelector().textContains(identifier));
                log("Element : " + identifier + " element : " + element.toString());
            } else if (findBy.equals(ID))
                element = new UiObject(new UiSelector().resourceIdMatches(identifier));
            else if (findBy.equals(CLASS_NAME))
                element = new UiObject(new UiSelector().className(identifier));
            else if (findBy.equals(DESCRIPTION))
                element = new UiObject(new UiSelector().descriptionMatches(identifier));
            if (uiObjectNotEmpty(element)) {
                break;
            }
            sleep(1000);
        }
        return element;
    }
    private boolean uiObjectNotEmpty(UiObject element) {
        return element != null && element.exists();
    }

    private boolean clickElement(UiObject element, String name) throws UiObjectNotFoundException {
        if (uiObjectNotEmpty(element)) {
            boolean clicked = element.click();
            out(name + " status : " + clicked, 0);
            return clicked;
        } else {
            throw new UiObjectNotFoundException(name + " not clickable");
        }
    }
}
