package com.browserstack.uiautomation.android.manufacturer;

import static android.os.SystemClock.sleep;

import androidx.test.uiautomator.By;
import androidx.test.uiautomator.UiObject;
import androidx.test.uiautomator.UiObject2;
import androidx.test.uiautomator.UiObjectNotFoundException;
import androidx.test.uiautomator.UiScrollable;
import androidx.test.uiautomator.UiSelector;
import androidx.test.uiautomator.Until;

import com.browserstack.uiautomation.android.Android;

import java.util.regex.Pattern;

public class Samsung extends Android {

    public String getAccountsText() {
        return "Accounts and backup";
    }

    public String getManageAccountsText() {
        return "Accounts";
    }

    public String getRemoveAccountsText() {
        return "Remove account";
    }

    public Pattern getSIMManagerText() {
        return Pattern.compile("SIM manager", Pattern.CASE_INSENSITIVE);
    }

    public String getSIMOnOffSwitchResourceId() {
        return "com.samsung.android.app.telephonyui:id/on_off_switch";
    }

    public String getSIMOnOffConfirmationButtonResourceId() {
        return "android:id/button1";
    }

    @Override
    public void goToAccounts() throws UiObjectNotFoundException {
        UiScrollable scrollable = new UiScrollable(new UiSelector().scrollable(true));

        final String ACCOUNTS_TEXT = getAccountsText();

        scrollable.scrollTextIntoView(ACCOUNTS_TEXT);
        mDevice.findObject(By.text(ACCOUNTS_TEXT)).click();
        sleep(1000);

        final String MANAGE_ACCOUNTS_TEXT = getManageAccountsText();

        mDevice.findObject(By.text(MANAGE_ACCOUNTS_TEXT)).click();
        sleep(1000);
    }

    @Override
    public void removeAllGoogleAccounts() throws UiObjectNotFoundException {
        final String REMOVE_ACCOUNT_TEXT = getRemoveAccountsText();

        while (mDevice.findObject(By.text("Google")) != null) {
            mDevice.findObject(By.text("Google")).click();
            mDevice.findObject(new UiSelector().textMatches(REMOVE_ACCOUNT_TEXT)).click();
            // Popup asking to confirm removal appears
            mDevice.findObject(new UiSelector().textMatches(REMOVE_ACCOUNT_TEXT)).click();
        }
    }

    @Override
    public void enableSim() throws Exception {
        openSIMManager();
        UiObject simToggleSwitch = mDevice.findObject(new UiSelector().resourceId(getSIMOnOffSwitchResourceId()).instance(0));
        simToggleSwitch.waitForExists(2000);
        if (simToggleSwitch.isChecked()) {
            return;
        }
        simToggleSwitch.click();

        UiObject simOnOffConfirmationButton = mDevice.findObject(new UiSelector().resourceId(getSIMOnOffConfirmationButtonResourceId()));
        simOnOffConfirmationButton.waitForExists(2000);
        simOnOffConfirmationButton.click();
    }

    @Override
    public void disableSim() throws Exception {
        openSIMManager();
        UiObject simToggleSwitch = mDevice.findObject(new UiSelector().resourceId(getSIMOnOffSwitchResourceId()).instance(0));
        simToggleSwitch.waitForExists(2000);
        if (!simToggleSwitch.isChecked()) {
            return;
        }
        simToggleSwitch.click();

        UiObject simOnOffConfirmationButton = mDevice.findObject(new UiSelector().resourceId(getSIMOnOffConfirmationButtonResourceId()));
        simOnOffConfirmationButton.waitForExists(2000);
        simOnOffConfirmationButton.click();
    }

    public void openSIMManager() {
        mDevice.findObject(By.text(getSIMManagerText())).click();
    }
}
