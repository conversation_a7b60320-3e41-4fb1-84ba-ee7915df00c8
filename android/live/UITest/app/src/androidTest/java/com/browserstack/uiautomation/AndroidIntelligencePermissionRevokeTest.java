package com.browserstack.uiautomation;

import static java.lang.Thread.sleep;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.uiautomator.By;
import androidx.test.uiautomator.UiObject;
import androidx.test.uiautomator.UiObject2;
import androidx.test.uiautomator.UiObjectNotFoundException;
import androidx.test.uiautomator.UiScrollable;
import androidx.test.uiautomator.UiSelector;

import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.IOException;
import java.util.ArrayList;
import java.util.regex.Pattern;

@RunWith(AndroidJUnit4.class)
public class AndroidIntelligencePermissionRevokeTest extends CommonTest {

    private final int FIND_ELEMENT_RETRIES = 10;
    private final String TEXT = "text";
    private final String ID = "id";
    private final String CLASS_NAME = "className";
    private final String DESCRIPTION = "description";
    private final String SEEALLAPPSMISSING = "could not locate see all apps in apps setions";
    private final String APPSMISSING = "could not locate Apps in Settings";
    private final ArrayList<String> APPS_TO_BLOCK = new ArrayList<String>(){{
        add("Call logs");
        add("Contacts");
        add("Phone");
        add("SMS");
    }};

    private final Pattern SEEALLAPPS = Pattern.compile("See all [0-9]+ apps");

    @Test
    public void revokePermissions() throws Exception {
        try {
            if (Build.VERSION.SDK_INT < 35) {
                startActivity("com.android.settings");
                UiScrollable window = new UiScrollable(new UiSelector().className("android.widget.FrameLayout"));
                UiObject appsUIObject = scrollToFindElement("Apps", FIND_ELEMENT_RETRIES, TEXT, window);
                if (uiObjectNotEmpty(appsUIObject)) {
                    appsUIObject.click();
                    UiObject2 seeAllApps = mDevice.findObject(By.text(SEEALLAPPS));
                    if(seeAllApps != null) {
                        seeAllApps.click();
                        UiObject moreOptions = scrollToFindElement("More options", FIND_ELEMENT_RETRIES, DESCRIPTION, window);
                        if(uiObjectNotEmpty(moreOptions)) {
                            moreOptions.click();
                            UiObject2 showSystem = mDevice.findObject(By.text("Show system"));
                            if(showSystem != null) {
                                showSystem.click();
                                UiScrollable appList = new UiScrollable(new UiSelector().resourceId("com.android.settings:id/apps_list"));
                                UiObject intelligenceApp = scrollToFindElement("Android System Intelligence", FIND_ELEMENT_RETRIES, TEXT, appList);
                                if(uiObjectNotEmpty(intelligenceApp)) {
                                    intelligenceApp.click();
                                }
                            }
                        }
                    }
                    else {
                        log("See All Apps not found");
                        throw new UiObjectNotFoundException(SEEALLAPPSMISSING);
                    }
                }
                else {
                    throw new UiObjectNotFoundException(APPSMISSING);
                }
            }

            UiScrollable window = new UiScrollable(new UiSelector().className("android.widget.FrameLayout"));
            UiObject permissions = scrollToFindElement("Permissions", FIND_ELEMENT_RETRIES, TEXT, window);
            if (uiObjectNotEmpty(permissions)) {
                permissions.click();
                UiScrollable appList = new UiScrollable(new UiSelector().resourceId("com.android.permissioncontroller:id/recycler_view"));
                for (String appName : APPS_TO_BLOCK) {
                    blockPermissions(appList, appName);
                }
            } else {
                throw new UiObjectNotFoundException("Permissions option not found in settings.");
            }
        }
        catch (Exception e) {
            out(e.getMessage(), 0);
            throw e;
        }
        finally {
            mDevice.pressHome();
            mDevice.executeShellCommand("am force-stop com.android.settings");
        }
    }

    private void blockPermissions(UiScrollable appList, String appName) throws Exception {
        UiObject contacts = scrollToFindElement(appName, FIND_ELEMENT_RETRIES, TEXT, appList);
        if(uiObjectNotEmpty(contacts)) {
            contacts.click();
            UiObject2 disallow = mDevice.findObject(By.text("Don’t allow"));
            if(!disallow.isChecked()) {
                disallow.click();
            }
            else {
                log("Disallow is already checked");
            }
            clickDontAllow();
            mDevice.pressBack();
        }
    }

    private void clickDontAllow() throws Exception {

        UiScrollable window = new UiScrollable(new UiSelector().className("android.widget.FrameLayout"));
        UiObject appsUIObject = scrollToFindElement("Don’t allow anyway", FIND_ELEMENT_RETRIES, TEXT, window);
        if(uiObjectNotEmpty(appsUIObject)) {
            appsUIObject.click();
            out("Apps Disallowed", 0);
        }
    }

    private UiObject scrollToFindElement(String identifier, int timeout, String findBy, UiScrollable container) throws InterruptedException {
        UiObject element = null;
        for (int i = 0; i < timeout; i++) {
            log("Finding element " + identifier + "by " + findBy + "; Try: " + i);
            element = findElement(identifier, findBy);
            if (uiObjectNotEmpty(element)) {
                break;
            }
            sleep(1000);
            try {
                log("Element " + identifier + " not found, scrolling.");
                container.scrollForward();
            } catch (UiObjectNotFoundException e) {
                e.printStackTrace();
            }
        }
        return element;
    }

    private UiObject findElement(String identifier, String findBy) throws InterruptedException {
        UiObject element = null;
        if (findBy.equals(TEXT)) {
            element = new UiObject(new UiSelector().textMatches(identifier));
            log("Element : " + identifier + " element : " + element.toString());
            if (element == null) {
                String newIdentifier = identifier.substring(0, 1).toUpperCase() + identifier.substring(1).toLowerCase();
                element = new UiObject(new UiSelector().textMatches(newIdentifier));
            }
        } else if (findBy.equals(ID)) {
            element = new UiObject(new UiSelector().resourceIdMatches(identifier));
        } else if (findBy.equals(CLASS_NAME)) {
            element = new UiObject(new UiSelector().className(identifier));
        } else if (findBy.equals(DESCRIPTION)) {
            element = new UiObject(new UiSelector().descriptionMatches(identifier));
        }
        return element;
    }

    private boolean uiObjectNotEmpty(UiObject element) {
        return element != null && element.exists();
    }
}
