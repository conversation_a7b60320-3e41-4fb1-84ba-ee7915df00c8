package com.browserstack.uiautomation.android;

import static android.os.SystemClock.sleep;

import androidx.test.uiautomator.By;
import androidx.test.uiautomator.BySelector;
import androidx.test.uiautomator.UiObject;
import androidx.test.uiautomator.UiObject2;
import androidx.test.uiautomator.UiObjectNotFoundException;
import androidx.test.uiautomator.UiScrollable;
import androidx.test.uiautomator.UiSelector;

import com.browserstack.uiautomation.CommonTest;

import java.io.IOException;

// TODO: Move Attributes like manufacturer, deviceVersion to this class
public class Android extends CommonTest{
    /*
    Abstract class for all android devices.
    It's implementations describe "how" to do any step.
    Test will have what to do in any test.

    Prerequisite for any request (function) is that the device is
    already on that screen, and able to handle the request
     */
    protected UiObject2 findObject(BySelector selector, int retries) {
        UiObject2 object = null;
        while (true) {
            object = mDevice.findObject(selector);
            if (object != null) return object;
            if (retries == 0) return null;
            sleep(100);
            retries--;
        }
    }

    public String getRemoveAccountText() {
        return "Remove account";
    }

    // Content-Desc of search icon in settings app - find with inspect in AL
    public String getSettingsSearchContentDesc() { return "Search settings"; }

    public String getRemovePlansText() { return "Remove all mobile plans"; }

    @Override
    public void startActivity(String package_name) throws IOException {
         // Keeping startActivity in CommonTest for backwards compatibility
         super.startActivity(package_name);
    }

    public void openSettingsApp() throws IOException {
        startActivity("com.android.settings");
    }

    public void goToAccounts() throws UiObjectNotFoundException {
        UiScrollable scrollable = new UiScrollable(new UiSelector().scrollable(true));

        scrollable.scrollTextIntoView("Accounts");
        mDevice.findObject(By.text("Accounts")).click();
        sleep(1000);
    }

    public void removeAllGoogleAccounts() throws UiObjectNotFoundException {
        String removeAccountText = getRemoveAccountText();
        // Remove all accounts
        while (mDevice.findObject(By.text("Google")) != null) {
            mDevice.findObject(By.text("Google")).click();
            mDevice.findObject(new UiSelector().textMatches(removeAccountText)).click();
            // Popup asking to confirm removal appears
            mDevice.findObject(new UiSelector().textMatches(removeAccountText)).click();
            sleep(1000);
        }
    }

    // Will bring you to page of setting passed
    public void searchSettingsAndClick(String setting) throws UiObjectNotFoundException {

        final String SETTINGS_SEARCH_DESC = getSettingsSearchContentDesc();
        final String SUBSTRING_SETTING_SEARCH = setting.substring(0, setting.length() - 1);

        while (mDevice.findObject(By.desc(SETTINGS_SEARCH_DESC)) == null) {
            sleep(100);
        }

        mDevice.findObject(By.desc(SETTINGS_SEARCH_DESC)).click();

        while (mDevice.findObject(By.text("Search")) == null) {
            sleep(100);
        }

        mDevice.findObject(By.text("Search")).setText(SUBSTRING_SETTING_SEARCH);

        while (mDevice.findObject(By.text(setting)) == null)  {
            sleep(100);
        }

        mDevice.findObject(By.text(setting)).click();
    }

    public void removeMobilePlans() throws UiObjectNotFoundException {
        final String REMOVE_PLANS_TEXT = getRemovePlansText();

        searchSettingsAndClick(REMOVE_PLANS_TEXT);

        UiScrollable scrollable = new UiScrollable(new UiSelector().scrollable(true));
        scrollable.scrollTextIntoView(REMOVE_PLANS_TEXT);

        while (mDevice.findObject(By.text(REMOVE_PLANS_TEXT)) == null) {
            sleep(100);
        }

        mDevice.findObject(By.text(REMOVE_PLANS_TEXT)).click();

        while (mDevice.findObject(By.text("Remove")) == null) {
            sleep(100);
        }

        sleep(1000);

        mDevice.findObject(By.text("Remove")).click();
    }

    public void enableSim() throws Exception {
        throw new RuntimeException("Method not implemented");
    }

    public void disableSim() throws Exception {
        throw new RuntimeException("Method not implemented");
    }


    public void goToHome() {
        mDevice.pressHome();
    }
}
