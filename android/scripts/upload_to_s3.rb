require 'aws-sdk-s3'
require 'dotenv/load'
require 'net/http'
require 'uri'
require_relative "../../common/push_to_zombie"

class UploadToS3
  def self.upload_zip_to_s3_via_presigned_url(path_to_file, upload_url)
    upload_url = URI.parse(upload_url)
    zip_file = File.open(path_to_file, "rb")
    response = Net::HTTP.start(upload_url.host) do |http|
      http.send_request("PUT", upload_url.request_uri, zip_file.read, "content_type" => "application/zip")
    end
    zip_file.close
  end

  def self.upload_file_to_s3( #rubocop:todo Metrics/ParameterLists
    id, key, content_type, path_to_file, acl, s3_url,
    unmutable_region=nil, timeout=0, storage_class='STANDARD', zip_logs=nil
  )
    # The in memory objects pointed by ARGVs are inmutable. creating a duplicate is a way around
    # so please duplicate any such variables that you wish to modify
    region = unmutable_region.dup unless unmutable_region.nil?
    status = false
    error = nil

    # Handling nil and empty checks
    storage_class ||= "STANDARD"
    storage_class = "STANDARD" if storage_class && storage_class.to_s.empty?
    zip_logs_flag = !zip_logs.nil? && zip_logs.to_s.downcase == 'true'

    begin
      #default region
      if region.nil? || region.empty?
        region = "us-east-1"
      else
        # Hack when $AWS_REGION is set to something like '-us-east-1'
        region.sub!(/^-/, '')
      end
      #default timeout for upload is 10 mins
      timeout = 600 if timeout.nil? || timeout == 0

      client = Aws::S3::Client.new(
        access_key_id: id,
        secret_access_key: key,
        region: region
      )

      ##need s3_url
      regex_result = s3_url.match(%r{.com/([a-zA-Z0-9-]+)/(.*)$}i).captures
      key_path = regex_result[1]
      bucket = regex_result[0]

      raise "key_path:#{key_path} or bucket: #{bucket} is nil" if key_path.nil? || bucket.nil?

      resp = nil
      s3_params = {
        acl: acl,
        bucket: bucket,
        key: key_path,
        content_type: content_type,
        storage_class: storage_class
      }

      s3_params[:content_encoding] = 'gzip' if zip_logs_flag.to_s.downcase == 'true' && path_to_file.match(/.gz/)

      Timeout.timeout(timeout) do
        File.open(path_to_file, 'rb') do |file|
          s3_params[:body] = file
          resp = client.put_object(s3_params)
        end
      end
      status = !resp.nil? && !resp[:etag].empty?
    rescue StandardError => e
      error = e.message
    end

    [status, error]
  end
end

if __FILE__ == $PROGRAM_NAME
  id = ARGV[0]
  key = ARGV[1]
  content_type = ARGV[2]
  path_to_file = ARGV[3]
  acl = ARGV[4]
  s3_url = ARGV[5]
  unmutable_region = ARGV[6]
  timeout = ARGV[7].to_i
  session_id = ARGV[8]
  genre = ARGV[9]
  storage_class = ARGV[10]
  zip_logs = ARGV[11] || false
  storage_class = 'STANDARD' if storage_class && storage_class.to_s.empty?
  status, error = UploadToS3.upload_file_to_s3(id, key, content_type, path_to_file, acl,
                                               s3_url, unmutable_region, timeout, storage_class, zip_logs)
  puts "Upload to S3 status: #{status} error: #{error} for session: #{session_id}"

  if error && session_id
    zombie_push("android", "s3-upload-error", "Error in uploading file #{path_to_file}", "android",
                error, "", session_id, "")
  end

  if status
    exit(0)
  else
    puts error
    exit(1)
  end
end
