require_relative '../common/helpers'
require_relative './version_managers/bs_screencap_manager'
require_relative './helpers/replugging_script'
require_relative './helpers/chrome_release_installer'
require_relative './helpers/rtcapp_release_helper'
require_relative './helpers/http_utils'
require_relative './helpers/popup_helper'
require_relative './helpers/cleanup_helper'
require_relative './helpers/rtc_helper'
require '/usr/local/.browserstack/mobile/common/helpers'
require 'logger'
require 'browserstack_utils'

require_relative './version_managers/ui_automation_apps_manager'
require_relative './version_managers/firefox_manager'
require_relative './version_managers/uc_browser_manager'
require_relative './version_managers/edge_manager'
require_relative './version_managers/samsung_browser_manager'
require_relative './version_managers/camera_check_manager'
require_relative './version_managers/browserstack_app_manager'
require_relative './version_managers/test_orchestrator_app_manager'
require_relative './version_managers/test_services_app_manager'
require_relative './version_managers/vpn_reverse_tether_app_manager'
require_relative './version_managers/play_services_manager'
require_relative './version_managers/play_store_manager'
require_relative './version_managers/device_owner_manager'
require_relative './version_managers/input_injector_apps_manager'
require_relative './version_managers/browserstack_watcher_manager'
require_relative './appium_app_manager'
require_relative './version_managers/googlemaps_manager'
require_relative './version_managers/facebook_app_manager'
require_relative './version_managers/talkback_manager'
require_relative './version_managers/google_photos_manager'
require_relative './version_managers/huawei_browser_manager'
require_relative './helpers/cleanup_function_runner'

require_relative './lib/grant_permission'
require_relative './exit_file'

class InstallApks # rubocop:disable Metrics/ClassLength

  attr_reader :device_id, :session_id

  def initialize(device_id, component, session_id, last_session_type, logger)
    @component = component
    @device_id = device_id
    @session_id = session_id
    @logger = logger
    @logger_params = {
      component: @component,
      session: @session_id,
      device: @device_id
    }

    @adb = AndroidToolkit::ADB.new(udid: @device_id, path: BrowserStack::ADB)
    AndroidToolkit::Log.logger = @logger

    @device_model = @adb.model
    @os_version = @adb.os_version.to_s
    @last_session_type = last_session_type
    @session_type = session_type
    @device_json = JSON.parse(read_with_lock(BrowserStack::CONFIG_JSON_FILE))['devices'][@device_id]
    @device_name = @device_json['device_name']
    @device_version = @device_json['device_version']
    @device_port = @device_json['port']
    @bs_screencap_manager = BsScreencapManager.new(@device_id, @logger)
    @static_conf = begin
      JSON.parse(File.read('/usr/local/.browserstack/config/static_conf.json'))
    rescue StandardError
      {}
    end
    @popup_helper = BrowserStack::PopupHelper.new(device_id: @device_id, os_version: Gem::Version.new(@os_version),
                                                  session_id: @session_id)
    @cleanup_function_runner = CleanupFunctionRunner.new(@device_id, @logger)
  end

  def device_obj
    @device_obj ||= BrowserStack::AndroidDevice.new(@device_id, self.class.to_s, @logger, @logger_params)
  end

  def root_command
    @root_command ||= RootCommand.new(device_obj, @logger, @logger_params)
  end

  def prerequisites
    unless device_obj.device_is_on_adb 5
      if @component == "cleanup"
        CleanupHelper.record_cleanup_failure_reason(@device_id,
                                                    "Device Off ADB during apk installation")
      end
      log :warn, "Device couldn't be found on ADB", cleanup_flow: true
      raise "Device couldn't be found on ADB"
    end

    disable_package_verifier

    disable_google_play_protect

    check_adb_connection  # replugging script

    disable_verify_adb_installs
  end

  def run
    prerequisites
    check_binary

    setup_popup_handler_playstore_login

    install_app_via_manager('ui_automation')
    install_app_via_manager('input_injector')
    install_app_via_manager('browserstack_watcher')

    grant_permission_for_custom_media

    install_app_via_manager('talkback')
    install_app_via_manager('google_photos')
    install_app_via_manager('browserstack_app')

    install_android_rtc_app

    # running PopUpHandlerTest / MainAutomation post installing RTC2 app
    @popup_helper.run_main_automation(exit_on_failure: false)
  end

  def install_camera_check_and_play_store
    @popup_helper.run_main_automation(exit_on_failure: false)

    install_app_via_manager('camera_check')
    @popup_helper.run_main_automation(exit_on_failure: false)

    install_app_via_manager('play_store')

  end

  def install_browsers_and_test_services
    install_app_via_manager('chrome', exit_on_failure: @component == "cleanup")
    install_app_via_manager('firefox', exit_on_failure: @component == "cleanup")
    install_app_via_manager('play_services', exit_on_failure: @component == "cleanup")
    install_app_via_manager('device_owner', exit_on_failure: @component == "cleanup")

    install_app_via_manager('test_orchestrator')
    install_app_via_manager('test_services')

    if @os_version.to_i < 10 && @device_model != "Nexus 9"
      install_app_via_manager('uc_browser')
      update_ucmobile_prefs
    end
  end

  def update_ucmobile_prefs
    @adb.shell("am start -n com.UCMobile.intl/com.UCMobile.main.UCMobile")
    @adb.push(BrowserStack::UCBROWSER_FLAGS, "/sdcard/flags_counter.sp")
    sleep 2
    root_command.run("cp /sdcard/flags_counter.sp /data/data/com.UCMobile.intl/shared_prefs/flags_counter.sp")
    @adb.shell("am force-stop com.UCMobile.intl")
  end

  def grant_permission_for_custom_media
    grant_permission = BrowserStack::GrantPermission.new(@device_id, @os_version, @logger)

    grant_permission.for_custom_media
  end

  def setup_popup_handler_playstore_login
    popup_handler_path = "#{BrowserStack::WEBRTC_DIR}/PopupHandlerAutomation.jar"
    playstore_login_path = "#{BrowserStack::WEBRTC_DIR}/google-play-store-login.jar"

    if File.exist?(popup_handler_path)
      cmd = "unzip -p #{popup_handler_path} META-INF/MANIFEST.MF | grep 'Manifest-Version'"
      existing_version = OSUtils.execute(cmd).split[1]
      cmd = "cat #{BrowserStack::BS_DIR}/mobile/popup-handler-automation/META-INF/MANIFEST.MF | grep 'Manifest-Version'"
      desired_version = OSUtils.execute(cmd).split[1]
      if existing_version != desired_version
        FileUtils.rm_f(popup_handler_path)
        fetch_from_s3('PopupHandlerAutomation.jar', popup_handler_path)
      end
    else
      fetch_from_s3('PopupHandlerAutomation.jar', popup_handler_path)
    end

    @adb.push(popup_handler_path, "/data/local/tmp/")
    @adb.push(playstore_login_path, "/data/local/tmp/")
  end

  def fetch_from_s3(s3_location, target_location)
    env = File.read("#{BrowserStack::BS_DIR}/env").strip
    bucket = 'bs-mobile'
    bucket = 'bs-mobile-stag' if env != 'prod'

    cmd = "cd #{BrowserStack::BS_DIR}/mobile && ~/bin/bundle exec aws_s3_wrapper --bucket #{bucket}"\
    " --remote_file android/#{s3_location} --local_file #{target_location}"
    result, status = OSUtils.execute(cmd)
    log :info, "S3 download status #{status} output #{result}"
  end

  def install_app_via_manager(app_name, exit_on_failure: true)
    manager = nil

    case app_name
    when 'ui_automation'
      manager = UIAutomationAppsManager.new(@device_id, @logger)
    when 'browserstack_watcher'
      manager = BrowserStackWatcherManager.new(@device_id, @logger)
    when 'input_injector'
      manager = InputInjectorAppsManager.new(@device_id, @logger)
    when 'talkback'
      manager = TalkbackManager.new(@device_id, @logger)
    when 'google_photos'
      manager = GooglePhotosManager.new(@device_id, @logger)
    when 'browserstack_app'
      manager = BrowserStackAppManager.new(@device_id, @logger)
    when 'facebook'
      manager = FacebookAppManager.new(@device_id, @logger)
    when 'google_maps'
      manager = GoogleMapsManager.new(@device_id, @logger)
    when 'huawei_browser'
      manager = HuaweiBrowserManager.new(@device_id, @logger)
    when 'camera_check'
      manager = CameraCheckManager.new(@device_id, @logger)
    when 'play_store'
      manager = PlayStoreManager.new(@device_id, @logger)
    when 'chrome'
      manager = ChromeReleaseInstaller.new(@device_id, @logger)
    when 'firefox'
      manager = FirefoxManager.new(@device_id, @logger)
    when 'play_services'
      manager = PlayServicesManager.new(@device_id, @logger)
    when 'device_owner'
      manager = DeviceOwnerManager.new(@device_id, @logger)
    when 'test_orchestrator'
      manager = TestOrchestratorAppManager.new(@device_id, @logger)
    when 'test_services'
      manager = TestServicesAppManager.new(@device_id, @logger)
    when 'uc_browser'
      manager = UCBrowserManager.new(@device_id, @logger)
    when 'samsung_browser'
      manager = SamsungBrowserManager.new(@device_id, @logger)
    when 'edge_browser'
      manager = EdgeManager.new(@device_id, @logger)
    when 'appium_manager'
      manager = AppiumAppsManager.new(@device_id, @logger)
    else
      log :info, "Unknown app_name given"
      return
    end

    log :info, "Verifying installation for #{app_name}"
    if manager.install_required?
      log :info, "     Installing #{app_name}", cleanup_flow: true
      manager.send(:ensure_install)
    end
  rescue StandardError => e
    log :error, "Installation failed for #{app_name} - #{e.message}"
    ExitFile.write(e.message)
    if @component == "cleanup"
      CleanupHelper.record_cleanup_failure_reason(@device_id, "Installation failed for #{app_name}")
    end
    raise e if exit_on_failure
  end

  def install_browsers
    install_app_via_manager('chrome')
    install_app_via_manager('firefox')
    install_app_via_manager('uc_browser')
    install_app_via_manager('samsung_browser')
    install_app_via_manager('edge_browser')
    install_app_via_manager('huawei_browser')
  end

  def check_adb_connection
    log :info, "     Checking adb connection", cleanup_flow: true
    log :info, "Starting check adb connection script (in case we did a full factory reset)"
    RepluggingScript.new(@device_id, @logger).start
  end

  def run_pre_final_steps
    if @os_version.to_i >= 10
      check_screen
      start_rtc_service
      @popup_helper.run_main_automation(exit_on_failure: false)
    end

    if @adb.manufacturer == "samsung" && @os_version.to_i >= 5
      disable_samsung_app_store
      install_app_via_manager('samsung_browser', exit_on_failure: @component == "cleanup")
    end

    stop_screen_stream

    install_app_via_manager('appium_manager')
    install_external_apks

    grant_permissions("common")
  rescue StandardError => e
    log :error, "Failed to install pre_final_steps - #{e.message}", cleanup_flow: true
    ExitFile.write(e.message)
    if @component == "cleanup"
      CleanupHelper.record_cleanup_failure_reason(@device_id, "Failed to install pre_final_steps")
    end
    raise "Failed to install pre_final_steps"
  end

  def call_unlock_screen
    if ["Redmi Note 8", "Pixel 2", "SM-A515F"].include?(@device_model)
      log :info, "CALL_UNLOCK_SCREEN: Unlock screen code begin"
      is_screen_on = begin
        @adb.shell("dumpsys power | grep mHoldingD | grep true")
      rescue StandardError
        ""
      end
      if !is_screen_on.empty?
        log :info, "CALL_UNLOCK_SCREEN: $DEVICEID screen is on, nothing to do"
      elsif @device_model == "Redmi Note 8"
        @adb.shell("input keyevent 26")
      else
        log :info, "CALL_UNLOCK_SCREEN: $DEVICEID screen is turned off, turning on"
        @adb.shell("input keyevent KEYCODE_WAKEUP")
      end

      is_keyguard_on = begin
        @adb.shell("dumpsys window | grep 'isStatusBarKeyguard' | grep true")
      rescue StandardError
        ""
      end
      if !is_keyguard_on.empty?
        log :info, "CALL_UNLOCK_SCREEN: $DEVICEID keyguard is on, disabling"
        @adb.shell("wm dismiss-keyguard")
      else
        log :info, "CALL_UNLOCK_SCREEN: $DEVICEID keyguard is dismissed, nothing to do"
      end
    elsif @os_version[0].to_s == "O" || @os_version.to_i >= 8 || ["Pixel", "Pixel 3"].include?(@device_model)
      # unlockoutput = $($ADB -s $DEVICEID shell "input keyevent KEYCODE_WAKEUP; wm dismiss-keyguard" 2>&1)
      unlockoutput = begin
        @adb.shell("input keyevent KEYCODE_WAKEUP; wm dismiss-keyguard")
      rescue StandardError => e
        e.message
      end
      log :debug, "unlockoutput - #{unlockoutput}"
      unless unlockoutput.empty?
        if File.exist?("#{BrowserStack::STATE_FILES_DIR}/session_#{@device_id}")
          zombie_key_value(
            platform: 'android',
            kind: 'screen-unlock-failed',
            error: "",
            browser: @device_model,
            data: "",
            device: @device_id,
            session: ""
          )
        else
          zombie_key_value(
            platform: 'android',
            kind: 'screen-unlock-failed-in-cleanup',
            error: "",
            browser: @device_model,
            data: "",
            device: @device_id,
            session: ""
          )
        end
      end
    else
      @adb.shell("am start com.android.browserstack/.main.UnlockScreen")
      sleep 1
      sleep 9 if @device_model == "Nexus 5"

      @adb.shell("input keyevent 3") # home button
      if @device_model == "SM-G950F"
        locked = begin
          @adb.shell("dumpsys window | grep -i mCurrentFocus | grep StatusBar") # Lock Screen
        rescue StandardError
          ""
        end
        @adb.shell("wm dismiss-keyguard") unless locked.empty?
      end
      device_obj.device_asleep?
    end
  end

  def check_screen # rubocop:todo Metrics/MethodLength
    log :info, "Checking screen", cleanup_flow: true
    @adb.put_setting("global", "stay_on_while_plugged_in", "3")
    wakefulness = begin
      @adb.shell("dumpsys power | grep 'mWakefulness'")
    rescue StandardError
      ""
    end
    called_unlock = false
    if wakefulness.empty?
      lock_state = begin
        @adb.shell("dumpsys power | grep 'mWakeLockState'").sub("mWakeLockState=", "").gsub(" ", "")
      rescue StandardError
        ""
      end
      user_state = begin
        @adb.shell("dumpsys power | grep 'mUserState'").sub("mUserState=", "").gsub(" ", "")
      rescue StandardError
        ""
      end
      if lock_state.empty? && user_state.empty?
        log :info, "Waking device"
        call_unlock_screen
        called_unlock = true
      end
    else
      state = begin
        @adb.shell("dumpsys power | grep 'mWakefulness'").sub("mWakefulness=", "").gsub(" ", "")
      rescue StandardError
        ""
      end
      log :info, "State: #{state[0, 6]}"

      # This variable is only present in devices with NFC (near field communication), but is a reliable indicator.
      nfc_screen_state = begin
        @adb.shell("dumpsys nfc | grep 'mScreenState='").chomp.split("=")[1]
      rescue StandardError
        ""
      end

      if state[0, 6] == "Asleep" || state[0, 6] == "Dozing" || nfc_screen_state == "ON_LOCKED"
        log :info, "Waking device"
        call_unlock_screen
        called_unlock = true
      end

      call_unlock_screen if !called_unlock && call_unlock_screen?(@device_id)

      if ["SM-G930F", "LG-H850", "HTC 10"].include?(@device_model)
        state = begin
          @adb.shell("dumpsys power | grep 'mWakefulness='").sub("mWakefulness=", "").gsub(" ", "")
        rescue StandardError
          ""
        end
        if state[0, 6] == "Asleep" || state[0, 6] == "Dozing"
          log :info, "Dismiss black screen"
          case @device_model
          when "SM-G930F"
            @adb.shell("input keyevent 3")
          when "LG-H850"
            @adb.shell("input keyevent 26; sleep 1;  input keyevent 26")
          when "HTC 10"
            @adb.shell("input keyevent 26")
          end
        end
      end
    end

    is_unlock_screen = begin
      @adb.shell("dumpsys window | grep 'mCurrentFocus' | grep 'UnlockScreen'")
    rescue StandardError
      ""
    end

    log :info, "Unlock screen state: #{is_unlock_screen}"
    state = begin
      @adb.shell("dumpsys power | grep 'mWakefulness'").sub("mWakefulness=", "").gsub(" ", "")
    rescue StandardError
      ""
    end
    log :info,  "Device State: #{state}"

    @adb.shell("input keyevent KEYCODE_BACK") unless is_unlock_screen.empty?

    # <MOB-3756 debug>
    # Adding this as I could not reproduce, so I needed more logging, and I may
    # as well retry the unlock screen if it failed to help the customers.
    is_the_phone_really_awake = begin
      @adb.shell("dumpsys power | grep 'mWakefulness='").sub("mWakefulness=", "").gsub(" ", "").include?("Awake")
    rescue StandardError
      false
    end
    is_the_keyguard_really_dismissed = begin
      @adb.shell("dumpsys window | grep 'isStatusBarKeyguard'").include?("false")
    rescue StandardError
      false
    end
    if @device_model == "SM-G960F" && (!is_the_phone_really_awake || !is_the_keyguard_really_dismissed)
      log :info, "The S9 failed to wake up, retrying"
      zombie_key_value(
        platform: 'android',
        kind: 'S9-failed-to-unlock',
        error: @device_id,
        browser: "",
        data: "",
        device: @device_model
      )

      # Retry
      call_unlock_screen

      # Verify
      is_the_phone_really_awake = begin
        @adb.shell("dumpsys power | grep 'mWakefulness='").sub("mWakefulness=", "").gsub(" ", "").include?("Awake")
      rescue StandardError
        false
      end
      is_the_keyguard_really_dismissed = begin
        @adb.shell("dumpsys window | grep 'isStatusBarKeyguard'").include?("false")
      rescue StandardError
        false
      end
      nfc_screen_state = begin
        @adb.shell("dumpsys nfc | grep 'mScreenState='").chomp.split("=")[1]
      rescue StandardError
        ""
      end
      log :info, "Phone is really awake? #{is_the_phone_really_awake}"
      log :info, "Keyguard really dismissed? #{is_the_keyguard_really_dismissed}"
      log :info, "NFC screen state: #{nfc_screen_state}"
    end
    # </MOB-3756 debug>
  end

  def start_rtc_service
    log :info, "Starting rtc service", cleanup_flow: true
    unless device_obj.uses_64bit_rtc_app?
      begin
        @adb.am("startservice --user 0 -n fr.pchab.AndroidRTC/.RTCService --es action \"foo\"")
      rescue StandardError => e
        log :error, "Couldn't start AndroidRTC - #{e.message}"
        zombie_key_value(
          platform: 'android',
          kind: 'start-failed-androidrtc',
          data: @device_model,
          device: @device_id,
          os_version: @os_version.to_s,
          session: @session_id
        )
      end
    end
    if RTCAppReleaseHelper.new.should_install_rtc_app?('v2', @device_model, @os_version.to_i)
      log :debug, "Starting rtc service for v2"
      begin
        @adb.am("startservice --user 0 -n fr.pchab.AndroidRTC2/.RTCService --es action \"foo\"")
      rescue StandardError => e
        log :error, "Couldn't start AndroidRTC2 - #{e.message}"
        zombie_key_value(
          platform: 'android',
          kind: 'start-failed-androidrtc2',
          data: @device_model,
          device: @device_id,
          os_version: @os_version.to_s,
          session: @session_id
        )
      end
    end
  end

  def disable_samsung_app_store
    log :debug, "Disabling samsung app store"
    if bsrun_device?(@device_id)
      root_command.run("pm disable com.samsung.android.securitylogagent; pm disable com.sec.android.app.samsungapps;")
    else
      @adb.shell("pm disable-user com.samsung.android.securitylogagent; "\
        "pm disable-user com.sec.android.app.samsungapps;")
    end
  end

  def stop_screen_stream
    return unless ["SM-N900", "SM-N9005"].include? @device_model

    log :debug, "Stopping screen stream app"
    @adb.shell("am force-stop sstream.app")
  end

  def install_external_apks
    # TODO: check me
    return unless @os_version.to_f >= 4.4

    log :info, "Installing external apks"
    install_app_via_manager('facebook')
    install_app_via_manager('google_maps')
  end

  def grant_permissions(target_group)
    log :info, "Granting permission to the target group - #{target_group}"
    grant_perm = BrowserStack::GrantPermission.new(@device_id, @os_version, @logger)
    case target_group
    when "custom_media"
      grant_perm.for_custom_media
    when "common"
      grant_perm.for_common_packages
    else
      log :error, "Invalid target group passed to grant_premisisons - #{target_group}"
      zombie_key_value(
        platform: 'android',
        kind: 'grant-permission-exception',
        data: "Invalid target group passed: #{target_group}",
        device: @device_id,
        os_version: @os_version.to_s,
        session: @session_id
      )
    end
  end

  def disable_verify_adb_installs
    log :info, "     Disabling adb installs verification", cleanup_flow: true
    cmd = "settings put global verifier_verify_adb_installs 0; "\
    "settings put global package_verifier_enable 0; "\
    "settings put system multi_window_enabled 0;"
    # TODO: Create and use cached bsrun_device?
    if bsrun_device?(@device_id)
      log :info, "Disabling verify adb install with root command : #{cmd}"
      root_command.run(cmd)
    else
      log :info, "Disabling verify adb install with adb shell"
      @adb.shell(cmd)
    end
  end

  def check_binary
    log :info, "     Trying install binaries", cleanup_flow: true
    retry_count = 0
    while retry_count < 5
      unless @bs_screencap_manager.install_required? || webrtc_install_required?
        log :info, "     bs_screencap and webrtc are installed on the device", cleanup_flow: true
        return
      end
      log :info, "     Attempting bs_screencap and webrtc installation for #{retry_count + 1} time", cleanup_flow: true
      @bs_screencap_manager.ensure_install
      install_webrtc_contents
      retry_count += 1
      sleep 1
    end
    log :warn, "     bs_screencap and webrtc binaries install failed", cleanup_flow: true
    if @component == "cleanup"
      CleanupHelper.record_cleanup_failure_reason(@device_id, "bs_screencap and webrtc binaries install failed")
    end
    raise "bs_screencap and webrtc binaries install failed"
  end

  def remove_existing_webrtc_contents_from_device
    @adb.shell("rm #{webrtc_device_dir}/InputInjector.jar #{webrtc_device_dir}/start_binary.sh "\
      "#{webrtc_device_dir}/busybox")
  rescue StandardError => e
    log(:warn, "some webrtc are not present - #{e.message}")
  end

  def install_android_rtc_app
    log :info, "     Installing android rtc app", cleanup_flow: true
    if device_obj.uses_64bit_rtc_app?
      apk_name = 'RTCActivity2_3_3_arm64.apk'
      apk_path = "#{BrowserStack::WEBRTC_DIR}/#{apk_name}"
      log :debug, "Installing #{apk_name}"
      unless File.exist?(apk_path)
        log :debug, "#{apk_name} not found, start download"
        fetch_from_s3(apk_name, apk_path)
      end
      arm64_apk = AndroidToolkit::APK.new(apk_path, aapt_path: BrowserStack::AAPT)
      rtc64_installer = ApkInstaller.new(arm64_apk, @device_id, @logger, @logger_params)
      install_rtc2_and_pre_start(rtc64_installer)
    else
      log :debug, "Installing RTCActivity.apk"
      apk_name = 'RTCActivity.apk'
      apk_path = "#{BrowserStack::WEBRTC_DIR}/#{apk_name}"
      if device_obj.uses_4_12_rtc_v1_app?
        apk_name = 'RTCActivity_4_12.apk'
        apk_path = "#{BrowserStack::WEBRTC_DIR}/#{apk_name}"
        unless File.exist?(apk_path)
          log :debug, "#{apk_name} not found, start download"
          fetch_from_s3(apk_name, apk_path)
        end
      end
      rtc_apk = AndroidToolkit::APK.new(apk_path, aapt_path: BrowserStack::AAPT)
      rtc_installer = ApkInstaller.new(rtc_apk, @device_id, @logger, @logger_params)
      rtc_installer.ensure_install # Verify package name as "fr.pchab.AndroidRTC"
      if RTCAppReleaseHelper.new.should_install_rtc_app?('v2', @device_model, @os_version)
        log :debug, "Installing RTCActivity2.apk"
        rtc2_apk = AndroidToolkit::APK.new(
          "#{BrowserStack::WEBRTC_DIR}/RTCActivity2.apk",
          aapt_path: BrowserStack::AAPT
        )
        rtc2_installer = ApkInstaller.new(rtc2_apk, @device_id, @logger, @logger_params)
        install_rtc2_and_pre_start(rtc2_installer)
      end
    end
  rescue StandardError => e
    log :error, "Failed to install android rtc app - #{e.message}", cleanup_flow: true
    ExitFile.write(e.message)
    if @component == "cleanup"
      CleanupHelper.record_cleanup_failure_reason(@device_id, "Failed to install android rtc app")
    end
    retry_cleanup("Failed to install android rtc app")
    raise "Failed to install android rtc app"
  end

  def install_rtc2_and_pre_start(apk_installer)
    pre_install_version = @adb.version_names(BrowserStack::RTCAPP_2_PACKAGE_NAME)
    apk_installer.ensure_install # Verify package name as "fr.pchab.AndroidRTC2"
    post_install_version = @adb.version_names(BrowserStack::RTCAPP_2_PACKAGE_NAME)

    # only executing in case when the app version changes
    if pre_install_version != post_install_version || @device_model == "BON-AL00"
      # invoking foo does not result in the old app popup being displayed
      # as a result, calling start and handling using PopUpHandlerTest / MainAutomation
      # old app popup only occurs when the app version changes
      # if handled once it never occurs as long as the app is not installed again
      # Always need this for BON-AL00 as it will request permission to draw over other apps
      log :debug, "Invoking start action for RTCService"
      @adb.am("startservice --user 0 -n fr.pchab.AndroidRTC2/.RTCService --es action \"start\"")
    end
    # this will allow the app to draw over other apps
    RtcHelper.new(@device_id, "install_apks").enable_popup_permission
    # triggering this again so that popup is generated
    if @device_model == "BON-AL00"
      @adb.am("startservice --user 0 -n fr.pchab.AndroidRTC2/.RTCService --es action \"start\"")
    end
  end

  def retry_cleanup(retry_reason)
    full_cleanup = "true"
    # Device can't do full cleanup, ensure retry is done as quick cleanup
    full_cleanup = "false" unless device_obj.full_reset_device?
    begin
      count = File.read("/tmp/#{@device_id}_cleanup_count").strip.to_i
    rescue StandardError
      count = 0
    end

    log :info, "Full cleanup count: #{count}"
    if count < 5
      log :info, "Issuing full cleanup"
      # TODO: push_to_influxdb_v1 "cleanup-retry" "cleanup" "cleanup" "$DEVICEID" "false" &
      BrowserStack::HttpUtils.send_get("http://localhost:45680/cleanup?device=#{@device_id}&full_cleanup=#{full_cleanup}&"\
                            "retry_cleanup=true&from=install_apks&reason=#{retry_reason}&check_and_clean=true")
      File.open("/tmp/#{@device_id}_cleanup_count", 'w') { |f| f.write((count + 1).to_s) }
    else
      log :error, "Multiple full cleanups attempted, not sending to cleanup"
    end
  end

  def webrtc_install_required?
    bin_list = @adb.shell("ls #{webrtc_device_dir}").split
    log(:info, "Found following binaries #{bin_list}")
    BrowserStack::WEBRTC_CONTENTS_LIST.each do |webrtc_binary|
      unless bin_list.include? webrtc_binary
        log(:info, "webrtc binary #{webrtc_binary} is not installed")
        return true
      end
    end

    log(:info, "All of the webrtc contents are installed")
    false
  rescue StandardError => e
    log(:warn, "some webrtc contents are not installed - #{e.message}")
    true
  end

  def install_webrtc_contents
    remove_existing_webrtc_contents_from_device

    BrowserStack::WEBRTC_CONTENTS_LIST.each do |webrtc_binary|
      binary_source_dir = BrowserStack::WEBRTC_DIR
      binary_source_dir = BrowserStack::LIVE_SCRIPTS_DIR if webrtc_binary == "start_binary.sh"
      @adb.push("#{binary_source_dir}/#{webrtc_binary}", webrtc_device_dir)
    end

    if device_obj.uses_64bit_busybox?
      log :info, 'Installing 64 bit busybox', cleanup_flow: true
      @adb.push("#{BrowserStack::WEBRTC_DIR}/busybox_64", "#{webrtc_device_dir}/busybox")
    end

    grant_permissions_to_webrtc_contents
  end

  def grant_permissions_to_webrtc_contents
    @adb.shell("chmod 755 #{webrtc_device_dir}/busybox")
    @adb.shell("chmod 755 #{webrtc_device_dir}/interactions_server")
    @adb.shell("chmod 755 #{webrtc_device_dir}/interactions-server-#{BrowserStack::INTERACTIONS_SERVER_VERSION}")
  end

  def webrtc_device_dir
    "/data/local/tmp"
  end

  def enable_google_play_protect
    update_package_verifier_user_consent(1)
  end

  def disable_google_play_protect
    log :info, "     Disabling google play protect", cleanup_flow: true
    update_package_verifier_user_consent(-1)
  end

  def update_package_verifier_user_consent(value)
    # enable or disable Google Play Protect settings by setting to 1 or -1
    @adb.shell("settings put secure package_verifier_user_consent #{value}")
    @adb.shell("settings put secure upload_apk_enable #{value}")
    @adb.shell("settings put global package_verifier_user_consent #{value}")
  end

  def enable_package_verifier
    update_package_verifier(1)
  end

  def disable_package_verifier
    log :info, "     Disabling package verifier", cleanup_flow: true
    update_package_verifier(0)
  end

  def update_package_verifier(value)
    @adb.shell("settings put global package_verifier_enable #{value}")
    @adb.shell("settings put global verifier_verify_adb_installs #{value}")
  end

  def install_honeyboard_app
    return unless @cleanup_function_runner.should_run?('install_honeyboard_app')

    log(:info, "Installing honeyboard app")
    @adb.install("#{BrowserStack::DEPS_DIR}/honeyboard/v1/honeyboard.apk", "-r")
  end

  private

  def session_type
    File.read("/tmp/sessionis_#{@device_id}").strip
  rescue StandardError
    ""
  end

  def log(level, msg, cleanup_flow: false)
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, msg) if cleanup_flow && @component == "cleanup"
    if @logger.instance_of?(Logger)
      formatted_msg = "#{self.class} #{msg}"
      @logger.send(level.to_sym, formatted_msg)
    else
      @logger.send(level.to_sym, msg, @logger_params)
    end
  end
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase.to_sym
  device_id = ARGV[1].to_s.strip
  component = ARGV[2].to_s.strip
  session_id = ARGV[3].to_s.strip
  last_session_type = ARGV[4].to_s.strip

  helper = InstallApks.new(device_id, component, session_id, last_session_type, Logger.new($stdout))
  helper.send(command)
end
