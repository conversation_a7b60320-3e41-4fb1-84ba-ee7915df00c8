require 'English'
require 'fileutils'
require 'ipaddr'
require 'socket'
require 'time'
require 'timeout'
require 'yaml'
require 'android_toolkit'
require 'browserstack_logger'
require 'device_fork_executor'
require 'static_conf'
require 'dry/monads'

require_relative '../common/helpers'
require_relative 'constants'
require_relative 'helpers/account_helper'
require_relative 'helpers/chrome_release_installer'
require_relative 'helpers/custom_media_manager'
require_relative 'helpers/device_browser_history'
require_relative 'helpers/http_utils'
require_relative 'helpers/camera_media_injector'
require_relative 'helpers/audio_injector'
require_relative 'helpers/media_manager'
require_relative 'helpers/rtcapp_release_helper'
require_relative 'helpers/system_update_block_helper'
require_relative 'helpers/usb_crash_report'
require_relative 'helpers/battery_instrumentor'
require_relative 'helpers/utils'
require_relative 'helpers/run_as_root_helper'
require_relative 'helpers/state_file_helper'
require_relative 'helpers/replugging_script'
require_relative 'lib/android_influxdb_client'
require_relative 'lib/model_database'
require_relative 'lib/recover_device'
require_relative 'lib/replugging_server_command'
require_relative 'lib/usb_vpn'
require_relative 'lib/bstack_reverse_tether_controller'
require_relative 'lib/browserstack_watcher_helper'
require_relative 'models/android_device'
require_relative 'models/device_specific_checks/battery_check'
require_relative 'models/device_specific_checks/systemui_check'
require_relative 'models/device_specific_checks/wifi_check'
require_relative 'version_managers/browserstack_app_manager'
require_relative 'version_managers/test_orchestrator_app_manager'
require_relative 'version_managers/huawei_browser_manager'
require_relative 'version_managers/test_services_app_manager'
require_relative 'version_managers/camera_check_manager'
require_relative 'version_managers/chrome_manager'
require_relative 'version_managers/edge_manager'
require_relative 'version_managers/device_owner_manager'
require_relative 'version_managers/firefox_manager'
require_relative 'version_managers/play_services_manager'
require_relative 'version_managers/play_store_manager'
require_relative 'version_managers/samsung_browser_manager'
require_relative 'version_managers/uc_browser_manager'
require_relative 'version_managers/ui_automation_apps_manager'
require_relative 'version_managers/vpn_reverse_tether_app_manager'
require_relative 'version_managers/gnirehtet_manager'
require_relative 'version_managers/bstack_reverse_tether_manager'
require_relative 'version_managers/browserstack_watcher_manager'
require_relative 'version_managers/restriction_scripts_manager'
require_relative 'appium_app_manager'
require_relative "helpers/browser_helper"
require_relative "helpers/device_sim_helper"
require_relative "helpers/google_pay_helper"
require_relative "version_managers/googlemaps_manager"
require_relative "version_managers/facebook_app_manager"
require_relative "version_managers/talkback_manager"
require_relative "helpers/rejoin_wifi_helper"
require_relative "component_controllers/device_owner_controller"
require_relative 'adb_checker'
require_relative "helpers/battery_helper"
require_relative 'helpers/cleanup_function_runner'

# For running a check on a single device
class DeviceThread # rubocop:todo Metrics/ClassLength
  include BrowserStack
  include Dry::Monads[:result]

  class DeviceCheckCannotRun < StandardError
  end

  class DeviceNotInConfig < StandardError
  end

  BATTERY_LOW_WATERMARK = 25
  BATTERY_HIGH_WATERMARK = 35

  SELENIUM_DEVICES = [
    'Nexus 6',
    'XT1092',
    'Nexus 9',
    'Pixel',
    'SM-G920I',
    'SM-G920F'
  ].freeze

  TIMEOUTCMD = RUBY_PLATFORM.include?('linux') ? 'timeout' : 'gtimeout'
  COMPONENT_VERSION_CONFIG_PATH = "#{BS_DIR}/mobile/config/component_versions.yml".freeze
  GET_URL_PATH = "#{BS_DIR}/mobile/android/live/scripts/getLastUrl.sh".freeze
  AAPT = Dir["#{BS_DIR}/android-sdk/build-tools/*/aapt"].max
  TELEPHONY_SERVICE_ENDPOINT = 'telephony-mobile-001-ec2-euw-prod.browserstack.com'.freeze
  NO_INSTALL_REASONS = [
    'Device under cleaning',
    'internet down:',
    'Restriction scripts not installed',
    'wifi not connected',
    'wifi connected but internet not working',
    'manual cleanup',
    'low battery',
    'incorrect boot mode',
    'mitm cert not installed',
    'media cleanup required',
    'injected mock media present',
    'Browserstack background service not running',
    'Browserstack service failed to restart',
    'manual', # Ignore all offline reasons which need manual intervention
    'uncaught exception', # Ignore all uncaught exception offlines, install phase cannot fix it
    'unlock failed',
    'screen off',
    'sdcard missing',
    'Account present',
    'no tun interface found, redovpn needed',
    OFFLINE_REASONS[:bstack_vpn_not_running],
    'Failed setting device_name',
    'need tether app as system app',
    'Device datetime incorrect',
    'ADB proxy not running',
    'adb unauthorized',
    'SystemUI not running', # Code does a reboot to fix this.
    'unknown firmware detected',
    OFFLINE_REASONS[:run_as_root_reboot_required],
    OFFLINE_REASONS[:run_as_root_rebooted],
    OFFLINE_REASONS[:run_as_root_manual_fix_required],
    OFFLINE_REASONS[:device_owner_not_enabled],
    OFFLINE_REASONS[:battery_level_not_found],
    OFFLINE_REASONS[:device_overheated]
  ].freeze

  def initialize(params)
    @port = params[:port]
    @device = params[:device]
    @ip = params[:ip]
    @hostname = params[:hostname]
    @device_data = params[:device_data]
    @machine_data = params[:machine_data]
    @logger_params = {
      device: params[:device],
      progname: File.basename(__FILE__)
    }
    @now = Time.now
    @cleanup_function_runner = CleanupFunctionRunner.new(@device, BrowserStack.logger)
    @influxdb_client = BrowserStack::AndroidInfluxdbClient.new(BrowserStack.logger, @logger_params)
    @device_obj = nil
    @adb = AndroidToolkit::ADB.new(udid: @device, path: BrowserStack::ADB)
    @device_state = DeviceState.new(@device)

    begin
      @device_obj = BrowserStack::AndroidDevice.new(@device, "DeviceThread", BrowserStack.logger)
    rescue BrowserStack::AndroidDevice::NoConfigFound => e
      BrowserStack.logger.info "Cannot use @device_obj as unable to load config for this device"
    end

    @replugging_server_command = BrowserStack::RepluggingServerCommand.new(@device_obj, BrowserStack.logger)
  end

  def static_config
    @static_config ||= StaticConf::StaticConfHelper.setup

  end

  def old_config
    @old_config ||= JSON.parse(read_file("#{CONFIG_DIR}/config.json") || "{}")
  end

  def devices_json_old
    @devices_json_old ||= old_config['devices'] || {}
  end

  def old_device
    @old_device ||= devices_json_old[@device] || {}
  end

  def devices_json
    @devices_json ||= Marshal.load(Marshal.dump(old_config))["devices"] || {}
  end

  def zombie
    @zombie ||= Zombie.new(
      os: 'android',
      browser: @device_obj.common_name,
      ip: @ip,
      region: static_config["sub_region"],
      browser_version: @temp_json["device_version"]
    )
  end

  def browser_config
    @browser_config ||= YAML.load_file(COMPONENT_VERSION_CONFIG_PATH)
  end

  def adb_devices
    @adb_devices ||= `#{BrowserStack::ADB} devices | grep device | grep -v List | awk '{print $1}'`.split("\n")
  end

  def device_unauthorized?
    @device_unauthorized ||= (`#{BrowserStack::ADB} devices | grep #{@device} | grep -cm1 "unauthorized"`.strip == '1')
  end

  def chrome_release_installer
    @chrome_release_installer ||= ChromeReleaseInstaller.new(
      @device,
      BrowserStack.logger,
      @logger_params
    )
  end

  def bstack_reverse_tether_controller
    @bstack_reverse_tether_controller ||= BStackReverseTetherController.new(@device_obj, BrowserStack.logger,
                                                                            @logger_params)
  end

  def device_owner_manager
    @device_owner_manager ||= DeviceOwnerManager.new(@device, BrowserStack.logger, @logger_params)
  end

  def account_helper
    @account_helper ||= AccountHelper.new(@device, BrowserStack.logger, BrowserStack.logger.params)
  end

  # Memoize battery level result, as it is used a few times
  def battery_level
    @battery_level ||= @device_obj.battery_level
  end

  def check_device_time_settings
    check_auto_time
    check_device_datetime
  end

  def check_auto_time
    auto_time = `#{BrowserStack::ADB} -s #{@device} shell settings get global auto_time`.strip
    `#{BrowserStack::ADB} -s #{@device} shell settings put global auto_time 1` unless auto_time == '1'
  end

  def check_device_datetime
    # Check if the device's datetime is correctly set
    if device_datetime_incorrect?
      @temp_json['online'] = false
      @temp_json['offline_reason'] = 'Device datetime incorrect'
    end
  end

  def device_datetime_incorrect?
    # Compare device and host times to check if device time is set in the future/past
    device_datetime_raw = `#{BrowserStack::ADB} -s #{@device} shell date`.strip
    # To avoid false positives as we sometimes get empty output
    return false if device_datetime_raw.empty?

    device_datetime = Time.parse(device_datetime_raw)
    host_datetime = Time.parse(`date`.strip)

    # Find the difference in days between device and host times
    days_difference_in_time = ((device_datetime - host_datetime).abs / 60 / 60 / 24).to_i

    BrowserStack.logger.info("Device time and host time difference #{@device}: #{days_difference_in_time}")

    days_difference_in_time > 10
  end

  def adb_proxy_running?
    `ps -ef | grep -v "grep" | grep #{ADB_PROXY_PATH} | grep #{ADB_PORT} | wc -l`.to_i > 0
  end

  def device_owner_controller
    @device_owner_controller ||= DeviceOwnerController.new(@device, BrowserStack.logger, @logger_params)
  end

  def check_adb_proxy
    return if adb_proxy_running?

    @temp_json['online'] = false
    @temp_json['offline_reason'] = 'ADB proxy not running'
  end

  def installed_version_package(app_name, deviceid = @device)
    `#{TIMEOUTCMD} 10 #{BrowserStack::ADB} -s #{deviceid} shell dumpsys package #{app_name}`
      .match(/versionName=([0-9.]*)/)[1]
  end

  def system_ui_popup_present?
    adb_device_shell = `
      #{TIMEOUTCMD} 10 #{BrowserStack::ADB} -s #{@device} shell dumpsys window |
      grep mCurrentFocus |
      grep com.android.systemui`
    current_focus = `
      #{TIMEOUTCMD} 10 #{BrowserStack::ADB} -s #{@device} shell dumpsys window |
      grep mCurrentFocus`
    BrowserStack.logger.info("dumpsys current focus #{current_focus}") #added for debugging
    BrowserStack.logger.info("dumpsys systemui popup #{adb_device_shell}")
    !adb_device_shell.empty?
  rescue StandardError => e
    BrowserStack.logger.info("Error running command #{e.message}")
    false
  end

  def restriction_scripts_installed?
    BrowserStack.logger.info "Checking if #{@device} has the latest restriction scripts installed"
    @logger_params[:component] = __method__.to_s

    raise "DEVICE EMPTY" if @device.nil? || @device.empty?

    restriction_scripts_manager = RestrictionScriptsManager.new(@device, BrowserStack.logger, @logger_params)

    if restriction_scripts_manager.install_required?
      @temp_json['online'] = false
      @temp_json['offline_reason'] = 'Restriction scripts not installed'
      return false
    end

    true
  end

  #TODO: use AndroidDevice#required_apps_missing to detect apps which aren't found
  def browserstack_apps_installed?
    @logger_params[:component] = __method__.to_s
    required_apps = ['com.android.browserstack', 'fr.pchab.AndroidRTC']
    required_apps -= ['fr.pchab.AndroidRTC'] if @device_obj.uses_64bit_rtc_app?

    use_rtc_app_v2 = RTCAppReleaseHelper.new.should_install_rtc_app?("v2", @device_data["device_name"],
                                                                     @device_data["device_version"])
    required_apps.push('fr.pchab.AndroidRTC2') if use_rtc_app_v2

    is_s6 = ["SM-G920F", "SM-G920I"].include?(@device_data['device_name'])
    return false if is_s6 && pm_list.empty?

    required_apps.each do |app_name|
      unless pm_list.include?(app_name) || pm_list.include?("package:#{app_name}")
        BrowserStack.logger.info("#{app_name} not found", @logger_params)
        return false
      end

      # Also check for version of app installed:
      begin
        installed_version = installed_version_package(app_name, @device)
      rescue StandardError
        installed_version = nil
      end

      # using special 64 bit webrtc for pixel 7 family and Pixel 6 Pro - v14 currently, MOBFR-678
      if app_name == 'fr.pchab.AndroidRTC2' && @device_obj.uses_64bit_rtc_app?
        next if installed_version.to_s == '3.3'
      elsif ["fr.pchab.AndroidRTC"].include?(app_name) && @device_obj.uses_4_12_rtc_v1_app?
        next if installed_version.to_s == '4.12'
      elsif ["fr.pchab.AndroidRTC", "fr.pchab.AndroidRTC2"].include?(app_name)
        next unless @machine_data[:versions].include?(app_name) &&
                    installed_version != @machine_data[:versions][app_name]
      else
        next unless @machine_data[:versions].include?(app_name) && version_less_than(
          installed_version, @machine_data[:versions][app_name]
        )
      end

      BrowserStack.logger.info(
        "app version for #{app_name} is #{installed_version} " \
        "but expected was #{@machine_data[:versions][app_name]}",
        @logger_params
      )
      return false
    end
    true
  end

  def vpn_tether_is_system_app?
    @logger_params[:component] = __method__.to_s
    bundle_name = 'com.google.android.vpntether'
    system_pm_list = `#{TIMEOUTCMD} 10 #{BrowserStack::ADB} -s #{@device} shell pm list packages -s`
    unless system_pm_list.include?(bundle_name) || system_pm_list.include?("package:#{bundle_name}")
      BrowserStack.logger.info("#{bundle_name} not found", @logger_params)
      return false
    end
    true
  end

  def version_less_than(version1, version2)
    Gem::Version.new(version1) < Gem::Version.new(version2)
  end

  def issue_full_cleanup(device_uid = @device)
    @logger_params[:component] = __method__.to_s

    begin
      count = File.read("/tmp/#{device_uid}_cleanup_count").strip.to_i
    rescue StandardError
      count = 0
    end

    BrowserStack.logger.info("Full cleanup count: #{count}")
    if count < 5
      BrowserStack.logger.info("issuing full cleanup", @logger_params)
      BrowserStack::HttpUtils.send_get("#{CLEANUP_ENDPOINT}?device=#{device_uid}&full_cleanup=true&"\
                            "retry_cleanup=true&from=device_check&reason=#{@full_cleanup_reason}&check_and_clean=true")
      File.open("/tmp/#{device_uid}_cleanup_count", 'w') { |f| f.write((count + 1).to_s) }
    else
      BrowserStack.logger.error("Multiple full cleanups attempted, not sending to cleanup")
    end
  end

  # TODO: URL escape reason
  def issue_cleanup(reason="unknown", check_and_clean: false)
    @logger_params[:component] = __method__.to_s
    BrowserStack::HttpUtils.send_get("#{CLEANUP_ENDPOINT}?device=#{@device}&full_cleanup=false&retry_cleanup=true&"\
           "from=device_check&reason=#{reason}&check_and_clean=#{check_and_clean}")
    BrowserStack.logger.info("issuing cleanup because of reason: #{reason}", @logger_params)
  end

  def flashed?(device_name)
    return true if @temp_json["device_version"].to_i == 11 # HOTFIX: Pixel 4 Android 11 device is flashed

    version = @temp_json['device_version'] == 'unknown' ? nil : @temp_json['device_version']
    is_bsrun_device = BrowserStack::ModelDatabase
                      .new(@temp_json['device_name'], version)
                      .property(:bsrun_device)

    return true unless is_bsrun_device

    begin
      adb_device_shell = "#{TIMEOUTCMD} 4 #{BrowserStack::ADB} -s #{@device} shell"
      num_flashed = `#{adb_device_shell} ls | grep -c browserstack`.to_i
      num_flashed += `#{adb_device_shell} ls /sbin/browserstack | grep -c browserstack`.to_i
      # This detects phones rooted like in some S9+, using a magisk module:
      num_flashed += `#{adb_device_shell} ls /bin/bdr | grep -c bdr`.to_i
      # Full end-to-end test
      bsrun_working = `sh #{PATH}/driver_actions.sh run_as_root #{@device} "whoami"`
                      .split("\n")
                      .map(&:strip)
                      .include?('root')
      num_flashed += 1 if bsrun_working
    rescue StandardError
      num_flashed = 0
    end
    flash_status = num_flashed != 0
    if device_name == "SM-G950F" && !File.exist?("#{CONFIG_DIR}/boot_img_version_#{@device}")
      begin
        boot_img_version = @adb.getprop("debug.bs.bootimage.version").to_i
      rescue StandardError
        boot_img_version = 0
      end
      File.write("#{CONFIG_DIR}/boot_img_version_#{@device}", boot_img_version)
    end
    flash_status
  end

  def vpn_ip
    ip_indx = @port - 8078
    "10.0.0.#{ip_indx}"
  end

  def vpn_running?
    @logger_params[:component] = __method__.to_s
    BrowserStack.logger.info("no tun - check", @logger_params)

    return true if UsbVPN.new(@device).vpn_tunnel_exists?

    BrowserStack.logger.warn("no tun - redoing vpn in next device check", @logger_params)
    false
  end

  def gnirehtet_relay_running?
    UsbVPN.new(@device).gnirehtet_relay_running?
  end

  def redo_vpn_required?
    return false if uses_wifi?(device_name, @temp_json["device_version"])
    return false unless RUBY_PLATFORM.include?("linux")

    # Redo vpn required if vpn not running.
    !vpn_running?
  end

  def was_offline?
    return false if devices_json_old[@device].nil?

    return true if devices_json_old[@device].empty?

    devices_json_old[@device]["online"].to_s == "false"
  end

  def old_full_offline_reason
    "genre: #{old_device['offline_reason']} cleanup_failure_reason: #{cleanup_failure_reason}"
  end

  def was_off_adb?
    return false if old_device.empty?

    [
      "device is not on usb",
      "device is on usb but not online on adb",
      "not on ADB",
      "device off adb"
    ].each do |reason|
      return true if old_full_offline_reason.include?(reason)
    end

    false
  end

  def install_phase_required?
    old_reason = devices_json_old[@device]['offline_reason'] || ''
    was_offline? && NO_INSTALL_REASONS.none?(&old_reason.method(:include?))
  end

  def pushed_unknown_firmware_file
    "#{STATE_FILES_DIR}/pushed_unknown_firmware_#{@device}"
  end

  def unknown_firmware?
    version = @temp_json['device_version'] == 'unknown' ? '' : @temp_json['device_version']
    firmware = @adb.getprop("ro.build.version.incremental") # present in all phones, gives the firmware
    known_builds = BrowserStack::ModelDatabase.new(device_name, version).property(:build_number)
    known_build_list = known_builds.split(",")
    if !known_build_list.include?(firmware)
      unless File.exist?(pushed_unknown_firmware_file)
        data = {
          'browser' => @temp_json['device_name'],
          'browser_version' => @temp_json["device_version"],
          'device' => @device
        }
        zombie.push_logs("unknown-firmware-detected", firmware, data)
        FileUtils.touch(pushed_unknown_firmware_file)
      end
    else
      FileUtils.rm_f(pushed_unknown_firmware_file)
      return false
    end
    true
  end

  def check_apps_and_vpn # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    @logger_params[:component] = __method__.to_s

    ui_automation_apps_manager = UIAutomationAppsManager.new(@device, BrowserStack.logger, @logger_params)
    browserstack_app_manager = BrowserStackAppManager.new(@device, BrowserStack.logger, @logger_params)
    vpn_reverse_tether_app_manager = VpnTetherAppManager.new(@device, BrowserStack.logger, @logger_params)
    gnirehtet_manager = GnirehtetManager.new(@device, BrowserStack.logger, @logger_params)
    bstack_reverse_tether_manager = BStackReverseTetherManager.new(@device, BrowserStack.logger, @logger_params)
    play_store_manager = PlayStoreManager.new(@device, BrowserStack.logger, @logger_params)
    browserstack_watcher_manager = BrowserStackWatcherManager.new(@device, BrowserStack.logger, @logger_params)
    test_orchestrator_app_manager = TestOrchestratorAppManager.new(@device, BrowserStack.logger, @logger_params)
    huawei_browser_manager = HuaweiBrowserManager.new(@device, BrowserStack.logger, @logger_params)
    test_services_app_manager = TestServicesAppManager.new(@device, BrowserStack.logger, @logger_params)
    camera_check_manager = CameraCheckManager.new(@device, BrowserStack.logger, @logger_params)
    appium_app_manager = AppiumAppsManager.new(@device, BrowserStack.logger, @logger_params)
    googlemaps_manager = GoogleMapsManager.new(@device, BrowserStack.logger, @logger_params)
    facebook_app_manager = FacebookAppManager.new(@device, BrowserStack.logger, @logger_params)
    talkback_manager = TalkbackManager.new(@device, BrowserStack.logger, @logger_params)
    usb_vpn = UsbVPN.new(@device)
    system_update_block_helper = SystemUpdateBlockHelper.new(@device, BrowserStack.logger, @logger_params)

    if browserstack_app_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:browserstack_app_not_installed])
    elsif !browserstack_apps_installed?
      @status = 'BrowserStack apps not installed'
    elsif appium_app_manager.install_required?
      @status = OFFLINE_REASONS[:appium_apps_not_installed]
    elsif play_store_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:play_store_not_installed])
    elsif ui_automation_apps_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:ui_automation_apps_not_installed])
    elsif check_media
      @status = 'media cleanup required'
    elsif injected_mock_media_present?
      @status = 'injected mock media present'
    elsif chrome_release_installer.install_required?
      mark_offline_with_initial_delay('installing new chrome release')
    elsif (outdated_browsers = check_all_browser_versions) && !outdated_browsers.empty?
      offline_reason = "outdated browser: #{outdated_browsers.join(',')}"
      mark_offline_with_initial_delay(offline_reason)
    elsif check_google_play_services
      mark_offline_with_initial_delay("outdated gms")
    elsif device_owner_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:device_owner_not_installed])
    elsif @temp_json['offline_reason'] == OFFLINE_REASONS[:device_owner_privileges_manual_fix]
      # Note, this must come before checking if device owner privileges enabled
      @status = OFFLINE_REASONS[:device_owner_privileges_manual_fix] # keep same offline reason
    elsif @device_obj.supports_device_owner? && !running_session? && !device_owner_manager.privileges_enabled?
      @status = OFFLINE_REASONS[:device_owner_not_enabled]
    elsif browserstack_watcher_manager.install_required?
      BrowserStack.logger.info("Watcher install required")
      mark_offline_with_initial_delay(OFFLINE_REASONS[:browserstack_watcher_not_installed])
    elsif camera_check_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:camera_check_app_not_installed])
    elsif !@device_obj.uses_bstack_internet_app? && @device_obj.needs_system_vpn_app? && !vpn_tether_is_system_app?
      @status = 'need tether app as system app'
    elsif !@device_obj.uses_bstack_internet_app? && !@device_obj.needs_system_vpn_app? &&
      vpn_reverse_tether_app_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:vpn_app_not_installed])
    elsif !@device_obj.uses_bstack_internet_app? && !usb_vpn.gnirehtet_vpn_authorized?
      @status = OFFLINE_REASONS[:gnirehtet_permission_not_handled]
    elsif !@device_obj.uses_bstack_internet_app? && gnirehtet_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:gnirehtet_app_not_installed])
    elsif !@device_obj.uses_bstack_internet_app? && usb_vpn.gnirehtet_relay_outdated?
      @status = OFFLINE_REASONS[:gnirehtet_relay_outdated]
    elsif bstack_reverse_tether_manager.app_install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:bstack_reverse_tether_app_not_installed])
    elsif bstack_reverse_tether_manager.forwarder_download_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:bstack_reverse_tether_forwarder_outdated])
    elsif !@device_obj.uses_bstack_internet_app? && redo_vpn_required?
      # if gnirethet app is running without relay,
      # internet is lost and the cleanup gets stuck at internet down and never does redovpn
      usb_vpn.stop_gnirehtet_app if @device_obj.uses_gnirehtet_vpn_app? && !gnirehtet_relay_running?
      @status = OFFLINE_REASONS[:no_tun_interface_found]
    elsif @device_obj.uses_bstack_internet_app? && !bstack_reverse_tether_controller.running?(full_check: false)
      @status = OFFLINE_REASONS[:bstack_vpn_not_running]
    elsif test_orchestrator_app_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:test_orchestrator_app_not_installed])
    elsif huawei_browser_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:huawei_browser_app_not_installed])
    elsif test_services_app_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:test_services_app_not_installed])
    elsif googlemaps_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:google_maps_not_installed])
    elsif facebook_app_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:facebook_app_not_installed])
    elsif talkback_manager.install_required?
      mark_offline_with_initial_delay(OFFLINE_REASONS[:talkback_app_not_installed])
    elsif system_update_block_helper.system_updates_blocked_file_outdated?
      @status = OFFLINE_REASONS[:system_updates_not_blocked]
    elsif unknown_firmware?
      @status = OFFLINE_REASONS[:unknown_firmware_detected]
    elsif !running_session? && !@device_obj.mitm_cert_installed?
      @status = 'mitm cert not installed'
    end
  end

  def check_google_play_services
    play_services_manager = PlayServicesManager.new(@device, BrowserStack.logger, @logger_params)
    play_services_manager.install_required?
  end

  def check_all_browser_versions
    # checks each browser on device to see if install is needed
    @logger_params[:component] = __method__.to_s
    installs_required = [
      # values in this array will be name of the browser if an install is needed
    ]

    chrome_manager = ChromeManager.new(@device, BrowserStack.logger, @logger_params)
    edge_manager = EdgeManager.new(@device, BrowserStack.logger, @logger_params)
    firefox_manager = FirefoxManager.new(@device, BrowserStack.logger, @logger_params)
    samsung_browser_manager = SamsungBrowserManager.new(@device, BrowserStack.logger, @logger_params)
    uc_browser_manager = UCBrowserManager.new(@device, BrowserStack.logger, @logger_params)

    installs_required << 'chrome' if chrome_manager.install_required?
    installs_required << 'edge' if edge_manager.install_required?
    installs_required << 'firefox' if firefox_manager.install_required?
    installs_required << 'samsung_browser' if samsung_browser_manager.install_required?
    installs_required << 'uc_browser' if uc_browser_manager.install_required?

    BrowserStack.logger.info("Browser installs needed: #{installs_required}")
    # mobile_chrome_version key is needed by Rails/DevTools
    @temp_json["mobile_chrome_version"] = chrome_manager.installed_version.to_s
    installs_required
  end

  def pm_list
    @logger_params[:component] = __method__.to_s
    return @pm_list unless @pm_list.nil?

    pm_list_out = `#{TIMEOUTCMD} 10 #{BrowserStack::ADB} -s #{@device} shell pm list packages`
    begin
      pm_list_arr = pm_list_out.strip.split
    rescue StandardError => e
      BrowserStack.logger.warn(
        "Exception while fetching pm_list." \
        "Retrying with encoding fix #{e.message} #{e.backtrace.join('\n')}",
        @logger_params
      )
      pm_list_arr = pm_list_out.force_encoding('iso-8859-1').encode('utf-8').strip.split
    end
    @pm_list ||= pm_list_arr.map { |x| x.gsub!('package:', '') }
  end

  def check_media
    @logger_params[:component] = __method__.to_s

    manager = MediaManager.new(@device, '/dev/null', BrowserStack.logger, @logger_params)

    if manager.media_cleanup_required?
      data = {
        'browser' => @temp_json['device_name'],
        'browser_version' => @temp_json["device_version"],
        'device' => @device
      }

      zombie.push_logs("bs-media-missing", "media cleanup required", data)
      return true
    end

    false
  end

  def injected_mock_media_present?
    @logger_params[:component] = __method__.to_s
    injected_mock_media_present = CameraMediaInjector.injected_mock_media_present?(@device)

    zombie.push_logs("camera-injection-cleanup-failed", "device: #{@device}") if injected_mock_media_present
    injected_mock_media_present
  end

  def check_default_appium_restart
    # nix commit revision check to restart appium server post deployment for default appium version
    @logger_params[:component] = __method__.to_s
    default_appium_version = get_default_appium_version(@temp_json["device_version"])
    appium_nix_commit_id = `cat #{BrowserStack::BS_DIR}/appium_#{default_appium_version}_bstack/commit_id`.strip

    unless @temp_json.key?("default_appium_commit_id")
      BrowserStack.logger.info("Initialising appium commit_id value for device config", @logger_params)
      @temp_json["default_appium_commit_id"] = ""
      return
    end
    if @temp_json["default_appium_commit_id"] == appium_nix_commit_id
      BrowserStack.logger.info("No revised commit found for appium deployment", @logger_params)
      return
    end
    BrowserStack.logger.info("Found revised commit, restarting appium server", @logger_params)
    should_wait_for_appium = "create_kill_and_wait"
    @temp_json["default_appium_commit_id"] = appium_nix_commit_id
    system(
      "sudo bash #{BS_DIR}/mobile/android/helpers/create_appium_service.sh #{@device} " \
      "#{@selenium_port} #{@chrome_driver_port} #{@android_bootstrap_port} #{default_appium_version} " \
      "#{should_wait_for_appium}"
    )
  end

  def check_appium(device_name)
    @logger_params[:component] = __method__.to_s
    # Do not ensure default version / kill appium here due to race condition:
    # If the device check is already started and session starts (before device_check completes)
    # START APPIUM SERVICE
    return if File.exist? "#{STATE_FILES_DIR}/session_#{@device}"

    begin
      process_running = `
        ps -ef |
        grep appium |
        grep node |
        grep #{@selenium_port} |
        grep #{@device} |
        grep -v grep |
        wc
      `.to_i
    rescue StandardError
      process_running = 0
    end
    should_wait_for_appium = SELENIUM_DEVICES.include?(device_name) ? "create_and_wait" : "create"
    if process_running == 0
      default_appium_version = get_default_appium_version(@temp_json["device_version"])
      BrowserStack.logger.info("Starting appium with appium_version: #{default_appium_version}", @logger_params)
      system(
        "sudo bash #{BS_DIR}/mobile/android/helpers/create_appium_service.sh #{@device} " \
        "#{@selenium_port} #{@chrome_driver_port} #{@android_bootstrap_port} #{default_appium_version} " \
        "#{should_wait_for_appium}"
      )
    end
  end

  def check_last_url
    # Checks if getLastUrl returns a url while the device is available
    # This means the browser history has not been cleaned properly, so we need to clear the browsers
    @logger_params[:component] = __method__.to_s
    # checks if the device is in use before fetching and clearing the browser.
    # Because at some times the device thread is started before the session and it runs when the session is running
    if running_processes?
      BrowserStack.logger.info(
        "Skipping check_last_url as the device is in session / cleaning / install phase #{@device}", @logger_params
      )
      return
    end
    return unless @temp_json['online']
    return unless @device_obj.rooted?

    BrowserStack.logger.info("Checking last url for online device #{@device}", @logger_params)

    device_browser_history = DeviceBrowserHistory.new(@device, BrowserStack.logger)
    result = device_browser_history.last_url.to_s.strip
    unless ["no history file found", ""].include? result
      BrowserStack.logger.info("Found last url: #{result} for available device #{@device}, clearing all browsers",
                               @logger_params)
      `#{BrowserStack::ADB} -s #{@device} shell <<__EOF
pm clear com.android.chrome
pm clear org.mozilla.firefox
pm clear com.sec.android.app.sbrowser
pm clear com.UCMobile.intl
pm clear com.microsoft.emmx
exit
__EOF`
    end

    # Full clean if clearing the browsers didn't work
    result = device_browser_history.last_url.to_s.strip
    unless ["no history file found", ""].include? result
      zombie.push_logs('device-browser-history', '', { 'device' => @device, 'data' => result })
      BrowserStack.logger.info(
        "Failed to clear last url: #{result} for available device #{@device}, doing full cleanup", @logger_params
      )
      @full_cleanup_reason = "failed_clear_last_url"
      issue_full_cleanup
    end
  end

  # Attempts to enable device owner privileges.
  # On failure, sends the device to cleanup.
  def fix_device_owner_not_enabled
    if device_owner_manager.privileges_enabled?
      log(:warn, "DeviceOwner privileges are already enabled")
      return
    end

    if device_owner_manager.install_required?
      raise 'Broken flow: enabling privileges impossible if device owner not installed.'
    end

    if account_helper.accounts_present?
      log(:warn, "Accounts present - DeviceOwner privileges can't be enabled")
      handle_account_present
      return
    end

    begin
      device_owner_manager.enable_privileges
    rescue AndroidToolkit::ADB::ExecutionError => e
      log(:warn, "Error enabling device owner: #{e.message}")
    end

    if device_owner_manager.privileges_enabled?
      log(:info, 'Device owner privileges enabled')
      return
    end

    log(:warn, 'DeviceOwner privileges not enabled')
    if @device_obj.full_reset_device?
      log(:info, 'Sending device to full cleanup')
      @full_cleanup_needed = true
      @full_cleanup_reason = "deviceowner_not_enabled"
      issue_full_cleanup
    else
      log(:info, 'Marking device offline for manual fix')
      @temp_json['online'] = false
      @temp_json['offline_reason'] = OFFLINE_REASONS[:device_owner_privileges_manual_fix]
    end
  end

  # Called from install phase
  def install_device_owner
    # Reinitializing device owner obj to pick up correct logger
    device_owner_manager = DeviceOwnerManager.new(@device, BrowserStack.logger, BrowserStack.logger.params)
    device_owner_manager.ensure_install
  rescue StandardError => e
    log(:error, "Failed to install device owner - sending to full cleanup: #{e.message}")
    @full_cleanup_needed = true
    @full_cleanup_reason = 'device_owner_failed_to_install'
    issue_full_cleanup
  end

  # Called from device check (fix_offline_reasons)
  # Handles Google / Samsung accounts by attempting to resolve quickly.
  # Failing that, it triggers install phase for which will run fix_account_present (UIAutomation)
  def handle_account_present
    BrowserStack.logger.info('Fix: Google or Samsung Account present')

    # Sometimes accounts cannot be removed from devices even by full cleanup.
    # In this case the only solution is a manual fix.
    if File.exist?(account_removal_manual_fix_file)
      @temp_json['online'] = false
      @temp_json['offline_reason'] = "manual fix: needs factory reset due to google account"
      return # ensure device does not go to install phase again
    end

    # Attempt to remove account quickly using device owner app
    result = account_helper.device_owner_remove_accounts
    return if result.success?

    BrowserStack.logger.warn("Device owner could not remove account: #{result.failure}")

    # Fixes take time (uiautomation / reboot)
    trigger_install_phase(OFFLINE_REASONS[:account_present])
  end

  # Since, the privoxy process doesn't run outside a session
  # We don't want devices to divert network traffic to privoxy during that time
  def unset_http_proxy
    BrowserStack.logger.warn("Unsetting global http proxy")
    @adb.shell("settings put global http_proxy :-1")
  end

  # Called from install phase
  # Fixes may involve ui automation and device reboot.
  # Failing that, it sends the device to full cleanup.
  def fix_account_present
    # This file ensures that if this method & full cleanup fails to remove account,
    # the device will be marked offline for a manual fix.
    FileUtils.touch(account_removal_manual_fix_file)

    # reinitializing account helper to pick up the install phase logger
    account_helper = AccountHelper.new(@device, BrowserStack.logger, BrowserStack.logger.params)
    samsung_account_present = account_helper.samsung_account_present? if @device_obj.samsung?
    account_helper.remove_accounts
    # If samsung account is present, the sync with Samsung Cloud option might have been enabled
    # This sync could possibly store some user data on the phone like Reminders, Browser bookmarks, etc
    # so it's better if we send the device to full cleanup
    if @device_obj.samsung? && samsung_account_present
      BrowserStack.logger.info("Samsung account found, sending device to full cleanup")
      @full_cleanup_reason = "samsung_account_present"
      issue_full_cleanup
    end
  rescue AccountRemovalError
    BrowserStack.logger.error("Failed to remove account, sending to full cleanup")
    @full_cleanup_reason = "account_present"
    issue_full_cleanup
  end

  def update_offline_reason_time(new_reason, old_reason, old_time)
    return '' if new_reason.empty?

    return @now if old_reason.empty? && !new_reason.empty?

    old_time = @now if old_time.nil? || old_time.empty?
    return old_time if new_reason == old_reason || (
      new_reason.include?("low battery") && old_reason.include?("low battery")
    )

    @now
  end

  def push_cleanup_failure_to_zombie(failure_reason, data={})
    old_failure_reason = (old_device || {})["cleanup_failure_reason"]

    if failure_reason != old_failure_reason
      zombie.push_logs("mobile-cleanup-failure", failure_reason, data)
      @influxdb_client.track_event('cleanup-failure', 'device_check', 'device_metrics', @device, 'true')
    end
  end

  def push_logs_to_zombie(kind, data={})
    @logger_params[:component] = __method__.to_s

    offline_reasons_to_exclude = [
      "Moved",
      "Device under cleaning"
    ]
    was_online = old_device.nil? || old_device["online"]
    valid_reason = offline_reasons_to_exclude.none? { |reason| kind.include?(reason) }

    new_offline_reason = kind.sub(/low battery.*/, "low battery").sub(/timeout.*/, "timeout")
    begin
      old_offline_reason = old_device["offline_reason"].sub(
        /low battery.*/, "low battery"
      ).sub(/timeout.*/, "timeout")
    rescue StandardError
      old_offline_reason = nil
    end

    return unless valid_reason

    if was_online || (new_offline_reason != old_offline_reason)
      @influxdb_client.track_event(
        'mobile-offline',
        'device_check',
        'push_logs_to_zombie',
        data['device'],
        'true'
      )
      zombie.push_logs("mobile-offline", new_offline_reason, data)
    end
  end

  def on_timeout(error)
    unless running_processes?
      system(
        "ps -ef |
         grep #{@device} |
         grep -v \"cleanup\|reboot\" |
         awk '{print $2}' |
         xargs sudo kill -9"
      )
    end

    temp_json = devices_json_old[@device] || {}
    adb_devices = `
      #{BrowserStack::ADB} devices |
      grep device |
      grep -v List |
      awk '{print $1}'
    `.split("\n")
    is_device_offline = `
      #{BrowserStack::ADB} devices |
      grep #{@device} |
      grep -cm1 "offline"
    `.strip == '1'
    if device_unauthorized?
      temp_json['offline_reason'] = 'adb unauthorized'
      publish_usb_crash_report
    elsif !adb_devices.include?(@device)
      temp_json['offline_reason'] = 'device off adb'
      publish_usb_crash_report
    elsif is_device_offline
      temp_json['offline_reason'] = 'device offline on adb'
      publish_usb_crash_report
    else
      temp_json['offline_reason'] = "timeout - #{error.backtrace[0]}" unless error.nil?
    end
    temp_json['online'] = false
    temp_json['os'] = 'android'
    temp_json['region'] = static_config["region"] || "unknown"
    temp_json['sub_region'] = static_config["sub_region"] || "unknown"
    temp_json['pool_mask'] ||= if DeviceSIMHelper.public_sim?(@device)
                                 POOL_MASK[:sim_enabled_device]
                               elsif AudioInjector.audio_injection_device?(@device)
                                 POOL_MASK[:audio_injection_device]
                               elsif GooglePay.google_pay_device?(@device)
                                 POOL_MASK[:google_pay_device]
                               else
                                 static_config['pool_mask']
                               end
    temp_json['port'] ||= @port
    temp_json['debugger_port'] ||= @port.to_s
    temp_json['dev_tool_port'] ||= "2#{@port}"
    temp_json['chrome_driver_port'] ||= "1#{@port}"
    temp_json['selenium_port'] ||= "3#{@port}"
    temp_json['ai_proxy_port'] ||= "3#{@port.to_i + AI_PROXY_PORT_OFFSET}"
    temp_json['playwright_port'] ||= "3#{@port.to_i + PLAYWRIGHT_ANDROID_PORT_OFFSET}"
    temp_json['cdp_port'] ||= "3#{@port.to_i + CDP_PORT_OFFSET}"
    temp_json['jackproxy_port'] ||= "4#{@port}"
    temp_json['jackproxy_https_port'] ||= "5#{@port}"
    temp_json['device_logger_port'] ||= "2#{@port.to_i + 400}"
    temp_json['android_bootstrap_port'] ||= temp_json['selenium_port'].to_i + 100
    temp_json['zotac_host'] ||= @hostname
    temp_json['ip'] ||= @ip
    temp_json['rails_endpoint'] = static_config.device_rails_endpoint(@device)
    {
      'device_version' => 'unknown',
      'device_name' => 'unknown',
      'mobile_ip' => 'mobile_ip',
      'device_serial' => @device
    }.each do |key, default|
      temp_json[key] = temp_json[key] && temp_json[key].strip == '' ? default : temp_json[key]
    end

    temp_json['offline_reason_time'] = if devices_json_old.key?(@device)
                                         update_offline_reason_time(
                                           temp_json['offline_reason'],
                                           devices_json_old[@device]['offline_reason'],
                                           devices_json_old[@device]['offline_reason_time']
                                         )
                                       else
                                         ''
                                       end
    `
      #{BS_DIR}/mobile/common/push_to_zombie.rb "android" "device-check-timeout"
      "#{temp_json['device_name']}" "#{error.backtrace[0]}" "#{@device}"
    `

    if adb_devices.include?(@device)
      @full_cleanup_reason = "device_check_timedout"
      issue_full_cleanup
    end
    temp_json
  end

  # Updates this device's status (offline reason) if another device has not been marked offline within
  # the last <delay_minutes>. Used to prevent marking all devices offline at the same time for the same reason
  # This gives device check some time to attempt fixing 1 device before marking the next offline
  def mark_offline_with_initial_delay(offline_reason, delay_minutes = 2)
    # If the device was already marked offline, it can be marked offline again immediately, no need to delay
    if was_offline?
      BrowserStack.logger.info("Device was offline, marking offline again with #{offline_reason}")
      @status = offline_reason
      return
    end

    File.open(BrowserStack::DEVICE_OFFLINE_LOCK_FILE, File::RDWR | File::CREAT) do |f|
      f.flock(File::LOCK_EX)

      if safe_to_mark_offline?(delay_minutes)
        @status = offline_reason

        notify_device_marked_offline # Lets the next Device Thread know a device has just been marked offline
        BrowserStack.logger.info("Safe to mark offline, marked device offline with #{offline_reason}")
      else
        BrowserStack.logger.info("Waiting before marking this device offline.")
      end
    end
  end

  def safe_to_mark_offline?(delay_minutes)
    # Status can be updated if another device has not been marked offline recently
    # If the lock file mtime is older than <delay_minutes> it's safe to update this device's status
    File.mtime(BrowserStack::DEVICE_OFFLINE_LOCK_FILE) < (Time.now - (60 * delay_minutes))
  end

  def notify_device_marked_offline
    # Indicates a device has just been marked offline
    File.write(BrowserStack::DEVICE_OFFLINE_LOCK_FILE, "") # Write to lock file to update mtime
  end

  def running_session?
    session_files = ["#{STATE_FILES_DIR}/session_#{@device}", "/tmp/duplicate_session_#{@device}"]
    session_files.any? { |session_file| File.exist?(session_file) }
  end

  def running_processes?
    cleanup_done_file = "#{STATE_FILES_DIR}/cleanupdone_#{@device}"
    session_files = ["#{STATE_FILES_DIR}/session_#{@device}", "/tmp/duplicate_session_#{@device}"]

    device_in_cleanup = File.exist?(cleanup_done_file)
    device_in_session = session_files.any? { |session_file| File.exist?(session_file) }
    device_in_install_phase = spawned_processes_running?

    BrowserStack.logger.info(
      "#{@device} activity status - "\
      "cleanup: #{device_in_cleanup}, "\
      "session: #{device_in_session}, "\
      "install_phase: #{device_in_install_phase}"
    )

    device_in_cleanup || device_in_session || device_in_install_phase
  end

  def frozen?
    # TODO: remove me. Keeping in case we really need it, but it's not cool to
    # make a function do 2 things.
    `timeout 2 adb -s #{@device} shell setprop "persist.sys.country US"`
    # Actual frozen check
    `timeout 2 adb -s #{@device} shell date`
    $CHILD_STATUS.exitstatus != 0

  end

  def cleanup_process_running?
    `ps -ef | grep "driver_actions.sh" | grep #{@device} | grep [c]leanup | wc -l`.chomp.to_i > 0
  end

  def adb_offline_reason(unclean = true) # rubocop:todo Style/OptionalBooleanParameter
    publish_usb_crash_report
    offline_on_adb = `
      #{BrowserStack::ADB} devices |
      grep #{@device} |
      grep -cm1 "offline"
    `.strip == "1"
    return "manual fix: low battery device off adb" if File.exist?("/tmp/unclean_low_battery_#{@device}")
    return "#{unclean ? 'unclean ' : ''}device off adb" unless offline_on_adb

    "#{unclean ? 'unclean ' : ''}device offline on adb"
  end

  def unclean_off_adb?
    @logger_params[:component] = __method__.to_s
    return false unless File.exist?("#{STATE_FILES_DIR}/unclean_off_adb_#{@device}")

    # If device is already recovered, send to cleanup don't have to do anything
    # else
    if @device_obj.connected?
      @temp_json["offline_reason"] = "Device under cleaning"
      BrowserStack.logger.info("unclean device back online. putting in cleanup", @logger_params)
      FileUtils.rm("#{STATE_FILES_DIR}/unclean_off_adb_#{@device}")
      issue_cleanup("unclean_device_back_online")
      return true
    end

    # Attempt to recover the device
    begin
      recover_device = RecoverDevice.new(@device_obj, BrowserStack.logger, @logger_params)
      recover_device.attempt
    rescue RecoverDevice::DeviceNotOnline, RecoverDevice::DeviceNotOnUSB => e
      @temp_json["offline_reason"] = e.message
    else
      @temp_json["offline_reason"] = "Device under cleaning"
      BrowserStack.logger.info("unclean device recovered now. putting in cleanup", @logger_params)
      FileUtils.rm("#{STATE_FILES_DIR}/unclean_off_adb_#{@device}")
      issue_cleanup("unclean_device_back_online_recovered")
    end

    true
  end

  # It will trigger cleanup again if not started
  # This is to speed up the recovery process.
  # Cleanup code will try to recover the device
  #
  # Rails will retry automatically in 30 minutes
  def cleanup_did_not_start?
    @logger_params[:component] = __method__.to_s

    if (@temp_json["cleanup_failure_reason"].to_s.match(/cleanup cannot run/) ||
        @temp_json["cleanup_failure_reason"].to_s.match(/not on ADB/)) &&
        # device was off adb when cleanup as attempted last time
       @temp_json["offline_reason"].to_s.match(/Device under cleaning/)

      BrowserStack.logger.info("Cleanup could not start because device was off adb, sending to cleanup", @logger_params)
      issue_cleanup("cleanup_did_not_start_off_adb", check_and_clean: true)
      return true
    end

    false
  end

  def needs_manual_factory_reset?
    manual_reset_file = "#{STATE_FILES_DIR}/needs_manual_factory_reset_#{@device}"
    return false unless File.exist?(manual_reset_file)

    full_reset_reason = File.read(manual_reset_file).strip

    @temp_json['offline_reason'] = "manual fix: needs factory reset #{full_reset_reason}"
    true
  end

  def unclean_frozen?
    @logger_params[:component] = __method__.to_s

    FileUtils.rm_f "/tmp/frozen_#{@device}" unless frozen?

    return false unless File.exist?("/tmp/frozen_#{@device}")

    # device fails frozen check when off abd
    if adb_devices.include?(@device) && frozen?
      @temp_json['offline_reason'] = 'frozen'
      @replugging_server_command.perform(:cancel)
      BrowserStack.logger.info('frozen', @logger_params)
    elsif !adb_devices.include?(@device)
      @temp_json['offline_reason'] = adb_offline_reason
      FileUtils.rm("/tmp/frozen_#{@device}")
      reset_cleanup_count
      FileUtils.touch("#{STATE_FILES_DIR}/unclean_off_adb_#{@device}")
    else
      @temp_json["offline_reason"] = 'Device under cleaning'
      BrowserStack.logger.info('frozen device back online, putting in cleanup', @logger_params)
      File.delete("/tmp/frozen_#{@device}")
      issue_cleanup("frozen_device_back_online")
    end

    true
  end

  def unclean_not_flashed?
    @logger_params[:component] = __method__.to_s
    if File.exist?("/tmp/unclean_not_flashed_#{@device}")
      @temp_json['offline_reason'] = 'Device not flashed'
      true
    else
      false
    end
  end

  # Returns false if the dumpsys battery command fails or hangs.
  # Memoises the result so that if the command hangs, it does not have to run twice
  def battery_level_found?
    return @battery_level_found unless @battery_level_found.nil?

    @battery_level_found = battery_level.is_a?(Integer)
  rescue BatteryLevelNotFound => e
    BrowserStack.logger.info("Battery level not found: #{e.message}")
    @battery_level_found = false
  end

  def unclean_low_battery?
    @logger_params[:component] = __method__.to_s
    return false unless File.exist?("/tmp/unclean_low_battery_#{@device}")

    unless adb_devices.include?(@device)
      @temp_json['offline_reason'] = adb_offline_reason
      FileUtils.touch("#{STATE_FILES_DIR}/unclean_off_adb_#{@device}")
      return true
    end

    # raising battery check for device availablility to 50 (MOBPE-386)
    if battery_level <= (BATTERY_HIGH_WATERMARK + 10)
      @temp_json['offline_reason'] = 'unclean low battery'
      BrowserStack.logger.info('unclean low battery', @logger_params)
    else
      @temp_json['offline_reason'] = 'Device under cleaning'
      BrowserStack.logger.info(
        'unclean low battery device back charged, putting in cleanup', @logger_params
      )
      reset_cleanup_count
      File.delete("/tmp/unclean_low_battery_#{@device}")
      issue_cleanup("unclean_low_battery_device_back_online")
    end

    true
  end

  def manual_cleanup?
    @logger_params[:component] = __method__.to_s
    if File.exist?("#{STATE_FILES_DIR}/manual_cleanupdone_#{@device}") &&
       File.exist?("#{STATE_FILES_DIR}/cleanupdone_#{@device}")
      @temp_json['offline_reason'] = 'manual cleanup'
      BrowserStack.logger.info('device cleanup triggered from machine', @logger_params)
      true
    else
      false
    end
  end

  def stuck_cleanup?
    @logger_params[:component] = __method__.to_s
    return false unless File.exist?("#{STATE_FILES_DIR}/cleanupdone_#{@device}") ||
                        File.exist?("/tmp/unclean_low_battery_#{@device}") ||
                        File.exist?("/tmp/frozen_#{@device}") ||
                        File.exist?("#{STATE_FILES_DIR}/unclean_off_adb_#{@device}")

    cleanup_age = (@now - File.stat("#{STATE_FILES_DIR}/cleanupdone_#{@device}").mtime).to_i
    return if cleanup_age <= 3600

    BrowserStack.logger.info('cleanup taking too long. Retrying', @logger_params)
    @temp_json['offline_reason'] = 'retry cleanup'
    issue_cleanup("retry_cleanup")
    true
  end

  def need_offline_detail?(temp_json)
    !cleanup_failure_reason.empty? &&
      (['manual cleanup', 'Device under cleaning'].include?(temp_json['offline_reason']) ||
       File.exist?("#{STATE_FILES_DIR}/cleanupdone_#{@device}"))
       # Also check the cleanupdone file because it offline_reason might not have been updated
  end

  def cleanup_failure_reason
    return '' unless File.size?(cleanup_failure_reason_file)

    # Migrate to new state file
    FileUtils.copy_file(cleanup_failure_reason_file, cleanup_failure_reason_state_file)

    full_reason = File.read(cleanup_failure_reason_state_file).strip
    full_reason[0..200]
  end

  # deprecated
  def cleanup_failure_reason_file
    "/tmp/cleanup_reason_#{@device}"
  end

  def cleanup_failure_reason_state_file
    "#{STATE_FILES_DIR}/cleanup_failure_reason_#{@device}"
  end

  def broken_cleanup?
    # each of these methods checks the broken cleanup conditions
    # and also fixes and updates offline_reason as required
    return true if unclean_off_adb?

    # Send device to cleanup if device back online and cleanup couldn't start
    # This is different from #unclean_off_adb? because that one checks the
    # `unclean_off_adb_<device-id>` file
    return true if cleanup_did_not_start?

    return true if unclean_frozen?

    return true if battery_level_found? && unclean_low_battery?

    return true if unclean_not_flashed?

    return true if needs_manual_factory_reset?

    false
  end

  def running_cleanup?
    return true if manual_cleanup?

    return true if stuck_cleanup?

    false
  end

  # Checks whether cleanup broke in the middle of its execution leading to an unclean device, mark
  # as manual cleanup or stuck in cleanup or as "Device under cleaning" if session/cleanup running
  def check_in_use_device
    @logger_params[:component] = __method__.to_s
    # Check for install phase before checking for broken cleanup
    # as the device may be legitimately off adb in install phase
    return if spawned_processes_running? || broken_cleanup? || running_cleanup?

    # TODO: Device not actually in cleaning here, because we return above, so
    # this offline reason can be modified
    @temp_json['offline_reason'] = 'Device under cleaning'
    @temp_json['active_session'] = File.exist?("#{STATE_FILES_DIR}/session_#{@device}").to_s
    BrowserStack.logger.info('device in use session/cleaning/rebooting', @logger_params)
  end

  def process_sdcard_status_file(_offline_reason_time)
    @logger_params[:component] = __method__.to_s

    begin
      @status = `#{BrowserStack::ADB} -s #{@device} shell cat /sdcard/status | tr -d '\r\n'`.strip
    rescue StandardError
      @status = ''
    end
    status_up_to_date = `#{BrowserStack::ADB} -s #{@device} shell /data/local/tmp/busybox find /sdcard/status -mmin -2`
                        .include? '/sdcard/status'
    if @status.include?('No such file or directory') || !status_up_to_date
      @status = 'Browserstack background service not running'
      if `#{BrowserStack::ADB} -s #{@device} shell "cat /sdcard/service_restarted"`.strip.to_i == 1
        @status = 'Browserstack service failed to restart'
      end

    else
      system("#{BrowserStack::ADB} -s #{@device} shell 'rm /sdcard/service_restarted'")
    end
  end

  def fetch_mobile_ip
    mobile_ip = `#{BrowserStack::ADB} -s #{@device} shell cat /sdcard/ipaddr`.strip
    begin
      ip_addr = IPAddr.new(mobile_ip)
    rescue StandardError
      ip_addr = nil
    end
    mobile_ip = @device_data['mobile_ip'] || 'nil' if ip_addr.nil?
    mobile_ip
  end

  def fetch_imsi
    @logger_params[:component] = __method__.to_s
    is_bsrun_device = BrowserStack::ModelDatabase
                      .new(@temp_json['device_name'], @temp_json['device_version'])
                      .property(:bsrun_device)

    imsi = if is_bsrun_device
             `
        sh #{PATH}/driver_actions.sh run_as_root #{@device} "service call iphonesubinfo 8" |
        awk -F "'" '{print $2}' |
        sed '1 d' |
        tr -d '.' |
        awk '{print}' ORS= 2>&1
      `.strip
           else
             fetch_imsi_from_bs_app
           end

    BrowserStack.logger.info(
      "new imsi: #{imsi.inspect}, old imsi: #{@temp_json['imsi'].inspect}", @logger_params
    )
    return imsi if @temp_json['imsi'] == imsi

    imsi_data = { 'imsi' => imsi, 'device' => @device }
    auth = { 'user' => static_config['telephony_user'], 'pass' => static_config['telephony_pass'] }
    BrowserStack.logger.info(
      "posting imsi_data to: #{TELEPHONY_SERVICE_ENDPOINT}, payload: #{imsi_data.inspect}",
      @logger_params
    )
    begin
      HttpUtils.send_post("http://#{TELEPHONY_SERVICE_ENDPOINT}/device", imsi_data, auth, nil, { read_timeout: 5 })
    rescue Exception => e
      BrowserStack.logger.error(
        "Error posting IMSI to telephony service! #{e.message} - #{e.backtrace}", @logger_params
      )
    end
    imsi
  end

  def fetch_imsi_from_bs_app
    @adb.am(
      "startservice --user 0 -n com.android.browserstack/.services.TelephonyHandlerService "\
      "--es action \"write-out-imsi\""
    )
    # This file won't exist in non-sim devices
    begin
      @adb.shell("cat /sdcard/imsi.prop").strip
    rescue StandardError
      ""
    end
  end

  def fetch_sim_details
    sim_details = []

    begin
      # When sim is disabled on device, device owner does not detect cellular subscriptions and sim details are sent as
      # empty.So before disabling SIM, all sim details are stored in as state file for use in this thread.
      if @device_obj.model == 'CN85'
        adb_output = @adb.shell("dumpsys telephony.registry | grep -m 1 mVoiceOperatorAlphaLong=")
        carriers = adb_output.split[5].split("=")[1]
      else
        carriers = @adb.getprop('gsm.sim.operator.alpha')
      end
      carriers = carriers.strip.split(',', 2)
      carriers.each_with_index do |carrier, carrier_index|
        next if carrier == ''

        sim_detail = {}
        sim_detail['carrier'] = carrier
        sim_detail['sim_slot'] = carrier_index + 1
        sim_detail['phone_number'] = "+#{@adb.shell('cat /sdcard/msisdn-0.prop')}" if @device_obj.model == 'CN85'
        sim_detail['imei'] =
          DeviceSIMHelper.fetch_sim_prop(@device, "imei-#{carrier_index}", true, 4, sim_detail['sim_slot'])
        sim_detail['iccid'] =
          DeviceSIMHelper.fetch_sim_prop(@device, "iccid-#{carrier_index}", true, 12, sim_detail['sim_slot'])
        next if sim_detail['imei'].empty? && sim_detail['iccid'].empty?

        sim_details.push(sim_detail)
      end
    rescue StandardError => e
      BrowserStack.logger.error("Failed to fetch sim_details or not a sim device #{e.message}")
      ""
    end
    sim_details
  end

  def pushed_replugging_server_configured_file
    "#{STATE_FILES_DIR}/replugging_server_#{@device}"
  end

  def check_recover_device_script_running
    if @device_obj.rooted?
      begin
        ps_output = @adb.shell("ps -ef| grep check_adb_connection_rooted.sh| grep -v grep")
        status = !ps_output.strip.empty?
      rescue AndroidToolkit::ADB::ADBError
        BrowserStack.logger.info("check_recover_device_script_running process not found, restarting script on device")
        RepluggingScript.new(@device_obj.id, BrowserStack.logger).start
        zombie.push_logs('android-recover-device', 'restart-replug-script', { 'device' => @device })
      end
    elsif @device_obj.os_version.to_i <= 10 && !File.exist?(File.join(pushed_replugging_server_configured_file))
      device_owner_controller.stop_poller
      FileUtils.touch(pushed_replugging_server_configured_file)
      begin
        process_kill_response = @adb.shell(
          "ps -ef | grep check_adb_connection.sh | grep -v grep | awk '{print $2}' | xargs kill -9",
          timeout: 10
        )
      rescue AndroidToolkit::ADB::ADBError
        BrowserStack.logger.info("check_recover_device_script_running process not found, DO will handle")
      end
    end
  end

  def check_device_connected
    return if @device_obj.connected?

    begin
      recover_device = RecoverDevice.new(@device_obj, BrowserStack.logger, @logger_params)
      recover_device.attempt
    rescue RecoverDevice::DeviceNotOnline, RecoverDevice::DeviceNotOnUSB => e
      raise DeviceCheckCannotRun, e.message
    rescue AndroidToolkit::ADB::TimeoutError => e
      raise DeviceCheckCannotRun, "manual fix required: usb controller crashed, reboot host"
    end
  end

  def slow_charging_popup_check_needed?
    @device_obj.manufacturer == 'samsung' && @device_obj.device_type == 'tablet'
  end

  def usb_popup_check_needed?
    ['CPH2333', 'CPH2035', 'CPH2495', 'CPH2251', 'RMX3085'].include?(@device_obj.model)
  end

  def mobile_plan_popup_check_needed?
    @device_obj.model.include?("SM-G99")
  end

  def popup_check_needed?
    slow_charging_popup_check_needed? || usb_popup_check_needed? || mobile_plan_popup_check_needed?
  end

  def check_and_dismiss_popup
    BrowserStack.logger.error("Handling check_and_dismiss_popup")
    if popup_check_needed? && system_ui_popup_present?
      BrowserStack.logger.error("Dismissing popup in device thread")
      if running_processes?
        BrowserStack.logger.info(
          "Skipping check_and_dismiss_popup,
          device is in session / cleaning / install phase #{@device}", @logger_params
        )
        return
      end
      @adb.shell("input keyevent KEYCODE_BACK")
      stats_data = usb_popup_check_needed? ? { 'popup_type': 'use_usb_popup' } : { 'popup_type': 'slow_charging_popup' }
      stats_data = { 'popup_type': 'mobile_plan_popup' } if mobile_plan_popup_check_needed?

      zombie.push_logs('device-check-popup', '', { 'device' => @temp_json['device_serial'], 'data' => stats_data })
    end
  end

  def push_battery_temperature
    # Do not push in every device check except for Samsung Galaxy S21, S22 & S23 of Mumbai Region
    return unless monitored_for_idle_time? || @temp_json['push_offline_to_zombie']

    temperature = @adb.battery_temperature
    zombie.push_logs('battery-temperature', '', { 'device' => @temp_json['device_serial'], 'data' => temperature })
  end

  def unblock_device_overheated_offline
    return unless overheated_offline?

    battery_helper = BrowserStack::BatteryHelper.new(@device, @adb.os_version.to_s, BrowserStack.logger)
    return unless battery_helper.unblock_device_overheated_offline?

    @temp_json['online'] = true
    @temp_json['last_online'] = @now
    @temp_json['offline_reason'] = ''
  end

  def overheated_offline?
    @temp_json['offline_reason'] == '' && was_overheated?
  end

  def block_overheated_device
    return if running_session?

    battery_helper = BrowserStack::BatteryHelper.new(@device, @adb.os_version.to_s, BrowserStack.logger)
    if currently_online? && battery_helper.block_overheated_device?
      @status = OFFLINE_REASONS[:device_overheated]
    elsif was_overheated? && !battery_helper.unblock_device_overheated_offline?
      @status = OFFLINE_REASONS[:device_overheated]
    end
  end

  def currently_online?
    old_device['online'] && @temp_json['offline_reason'] == ''
  end

  def was_overheated?
    old_device['offline_reason'] == OFFLINE_REASONS[:device_overheated]
  end

  def monitored_for_idle_time?
    samsung_tier1_devices = ['Samsung Galaxy S21', 'Samsung Galaxy S22', 'Samsung Galaxy S23']
    monitored_regions = ['ap-south-1']

    return false unless monitored_regions.include? @temp_json['region']

    return false unless samsung_tier1_devices.any? { |name| @device_obj.common_name.match?(/#{Regexp.escape(name)}/) }

    true
  end

  def device_current_state
    cleanup_done_file = "#{STATE_FILES_DIR}/cleanupdone_#{@device}"
    session_files = ["#{STATE_FILES_DIR}/session_#{@device}", "/tmp/duplicate_session_#{@device}"]

    return "cleanup" if File.exist?(cleanup_done_file)

    return "session" if session_files.any? { |session_file| File.exist?(session_file) }

    return "install_phase" if spawned_processes_running?

    "idle"
  end

  def instrument_and_push_battery_temperature
    battery_helper = BrowserStack::BatteryHelper.new(@device, @adb.os_version.to_s, BrowserStack.logger)
    battery_helper.instrument_and_push_battery_temperature(device_current_state, get_session_id(@device))
  rescue AndroidToolkit::ADB::ADBError => e
    BrowserStack.logger.info("Received ADB error for device #{@device}: #{e.message}")
  end

  def enable_battery_life_optimizer
    battery_helper = BrowserStack::BatteryHelper.new(@device, @adb.os_version.to_s, BrowserStack.logger)
    battery_helper.battery_life_optimizer
  end

  def ensure_watcher_running
    browserstack_watcher_manager = BrowserStackWatcherManager.new(@device, BrowserStack.logger, @logger_params)
    unless browserstack_watcher_manager.install_required?
      BrowserStack.logger.info("Watcher is installed, checking if it's need to be started")
      browserstack_watcher_helper = WatcherHelper.new(@device, "", "", BrowserStack.logger)
      output = browserstack_watcher_helper.check_if_running
      if output.nil?
        BrowserStack.logger.info("Watcher is not running, starting it")

        data = {
          'browser' => @temp_json['device_name'],
          'browser_version' => @temp_json["device_version"],
          'device' => @device
        }
        zombie.push_logs("watcher-service-starting", "starting watcher service from device check while idle", data)
        browserstack_watcher_helper.start_from_cleanup
      end
    end
  end

  def grant_write_settings_bs_app
    browserstack_app_manager = BrowserStackAppManager.new(@device, BrowserStack.logger, @logger_params)
    browserstack_app_manager.grant_bs_write_settings_via_appops
  rescue StandardError => e
    BrowserStack.logger.info("Unable to grant bs apk write settings permission: #{e.message}")
  end

  def enable_selenium_preference
    @device_obj.os_version.to_i > 10
  end

  def generate_selenium_preference
    return battery_level unless enable_selenium_preference

    battery_temperature = @adb.battery_temperature
    default_battery_temperature = 30

    # preference to decrease when temperature increases or level decreases
    preference = if battery_temperature.nil?
                   -1 * default_battery_temperature
                 else
                   -1 * battery_temperature
                 end

    BrowserStack.logger.info("battery: #{battery_level}, temp: #{battery_temperature}, preference: #{preference}")

    preference
  end

  def setup_config
    is_device_name_known = @temp_json['device_name'] && @temp_json['device_name'] != 'unknown'
    version = @adb.os_version.to_s
    # os_version will return 0 if version could not be found. Instead set this to blank string
    version = '' if version == '0'
    device_serial = @temp_json['device_serial'] || @adb.getprop("ril.serialnumber")
    device_serial = @device if device_serial == ''
    mobile_ip = fetch_mobile_ip

    @temp_json['device_serial'] = device_serial

    last_known_version = @temp_json['device_version'] == '' ? 'unknown' : @temp_json['device_version']
    @temp_json['device_version'] = version && version.strip == '' ? last_known_version : version
    @temp_json['device_name'] = is_device_name_known ? @temp_json['device_name'] : adb_get_device_name
    @temp_json['mobile_ip'] = mobile_ip && mobile_ip.strip == '' ? 'mobile_ip' : mobile_ip
    @temp_json['battery_level'] = generate_selenium_preference if battery_level_found?
    @temp_json['vendor_build_date'] = vendor_build_date
    @temp_json['device_resolution'] =
      valid_device_info?(@temp_json['device_resolution']) ? @temp_json['device_resolution'] : device_resolution
    @temp_json['device_ram'] =
      valid_device_info?(@temp_json['device_ram']) ? @temp_json['device_ram'] : device_ram
    @temp_json['device_storage'] =
      valid_device_info?(@temp_json['device_storage']) ? @temp_json['device_storage'] : device_storage
    @temp_json['device_chipset'] =
      valid_device_info?(@temp_json['device_chipset']) ? @temp_json['device_chipset'] : device_chipset
  end

  def check_and_fix_interaction_args(device_name)
    interaction_args_file = "#{CONFIG_DIR}/calculated_interaction_args_#{@device}"

    recalculate_interaction_args = recalculation_required?(interaction_args_file)

    return unless recalculate_interaction_args

    args, = interaction_args_and_stream_width(device_name)

    if recalculate_interaction_args
      BrowserStack.logger.info('Interactions config modified. Recalculating interaction args')
      system("bash #{LIVE_SCRIPT} calculate_interaction_args \"#{@device}\" \"#{args}\"")
    end
  rescue StandardError => e
    BrowserStack.logger.error("Error checking/recalculating interaction args: #{e.message} - #{e.backtrace}")
  end

  def recalculation_required?(calculated_file)
    return true unless File.exist?(calculated_file)

    config_mtime = File.mtime(INTERACTION_CONFIG_FILE)
    file_mtime = File.mtime(calculated_file)

    return true unless file_mtime > config_mtime

    false
  end

  def interaction_args_and_stream_width(device_name)
    device_display_name = BrowserStack::ModelDatabase.new(device_name).property(:display_name)
    device_display_name = device_display_name.downcase

    interaction_json = JSON.parse(File.read(INTERACTION_CONFIG_FILE))
    interaction_json = interaction_json["deviceConfigurations"]
    params = interaction_json[device_display_name] || interaction_json['default']
    interaction_args = " #{params['touch_need_btn_touch_event'].to_i}" \
                       " #{params['touch_avg_pressure'].to_i}" \
                       " #{params['touch_avg_contact_size'].to_i}" \
                       " #{params['touch_avg_finger_size'].to_i}" \
                       " #{params['touch_max_track_id'].to_i}"
    interaction_args.gsub!('null', '0')

    stream_width = DEFAULT_STREAM_WIDTH
    stream_width = params['stream_width'] if params.key?('stream_width')

    [interaction_args, stream_width]
  rescue StandardError => e
    BrowserStack.logger.error("Error getting interaction args: #{e.message} - #{e.backtrace}")
    raise e
  end

  def sdcard_missing?
    sdcard_out = `#{BrowserStack::ADB} -s #{@device} shell ls /sdcard 2>&1`
    sdcard_out.include?("No such file or directory")
  end

  # Checks if install phase is running for device
  def spawned_processes_running?
    @logger_params[:component] = __method__.to_s

    # Add checks for different spawned processes here..
    # install-phase is spawned here: #trigger_install_phase
    install_phase_executor = DeviceForkExecutor.new(@device, INSTALL_PHASE[:name],
                                                    STATE_FILES_DIR, BrowserStack.logger, @logger_params)

    if install_phase_executor.process_running?
      BrowserStack.logger.info("install-phase is still running", @logger_params)
      # Need to set an offline reason here, otherwise `fix_offline_reason` won't be invoked.
      @temp_json['offline_reason'] = 'install-phase is still running'

      install_phase_time_bound = INSTALL_PHASE[:timeout] # seconds
      if Time.now - install_phase_executor.state[:started_at] > install_phase_time_bound
        BrowserStack.logger.warn("install-phase has been running for more than #{install_phase_time_bound} seconds, "\
                                 "killing it assuming it hanged. Issuing full cleanup", @logger_params)
        install_phase_executor.reset!
        @full_cleanup_needed = true
        @full_cleanup_reason = "install_phase_stuck"

        zombie.push_logs('install-phase-stuck', '', { 'device' => @device })
        return false
      end

      true
    else
      BrowserStack.logger.info("install-phase is not running", @logger_params)
      false
    end
  end

  # Checks to run are a parameter, to allow DI via rspecs
  # This method sets the @status variable which is then set as the
  # offline_reason of the device
  def run_device_checks(checks_to_run = [BatteryCheck, SystemUICheck])
    @logger_params[:component] = __method__.to_s

    prev_offline_reason = begin
      devices_json_old[@device]['offline_reason']
    rescue StandardError
      nil
    end

    checks_to_run.each do |klass|
      check = klass.new(@device_obj, BrowserStack.logger, @logger_params)

      begin
        check.run(prev_offline_reason)
        BrowserStack.logger.info("Check for #{klass} passed")
      rescue StandardError => e
        reason = if e.instance_of?(DeviceSpecificCheck::Failed)
                   e.message
                 else
                   "uncaught exception for #{klass}: #{e.message}"
                 end

        BrowserStack.logger.error("Check for #{klass} failed, marking device offline "\
                                  "with #{reason}, #{e.backtrace.join("\n")}")
        @status = reason

        # No need to run further checks if one failed
        break
      end
    end
  end

  def check_offline_device
    process_sdcard_status_file(@temp_json["offline_reason_time"])

    unless @device_obj.correct_boot_mode?
      @temp_json['online'] = false
      @temp_json['offline_reason'] = 'incorrect boot mode'
      return
    end

    unless flashed?(@temp_json['device_name'])
      @temp_json['online'] = false
      @temp_json['offline_reason'] = 'device is not flashed'
      return
    end

    if sdcard_missing?
      @temp_json['online'] = false
      @temp_json['offline_reason'] = 'sdcard missing'
      return
    end

    unless battery_level_found?
      @temp_json['online'] = false
      @temp_json['offline_reason'] = OFFLINE_REASONS[:battery_level_not_found]
      return
    end

    if @temp_json['device_name'] == 'unknown'
      @temp_json['online'] = false
      @temp_json['offline_reason'] = 'Failed setting device_name'
      return
    end

    run_device_checks

    # TODO: Figure out where status is being set above, and just set the offline
    # reason directly there (process_sdcard_status_file).
    # ---
    # If any of the device checks fail, mark the device offline immediately
    # instead of doing further checks below
    if @status.to_s != ''
      @temp_json['offline_reason'] = @status
      @temp_json['online'] = false
      return true
    end

    run_device_checks(checks_to_run = [WifiCheck])
    version = @temp_json['device_version'] == 'unknown' ? '' : @temp_json['device_version']
    check_apps_and_vpn

    # Needs to come after check_apps_and_vpn, which ensures that device owner
    # and uiautomator apps are installed (required for fixing account present).
    # @status must be empty - otherwise this will overwrite
    # the offline reason set in check_apps_and_vpn
    if @status == '' && account_helper.accounts_present?
      @temp_json['online'] = false
      @temp_json['offline_reason'] = OFFLINE_REASONS[:account_present]
      return
    elsif File.exist?(account_removal_manual_fix_file)
      # If no accounts are found but this file still exists, hosting have
      # probably fixed the device manually - this file can be removed.
      FileUtils.rm_f(account_removal_manual_fix_file)
    end

    block_overheated_device
    @temp_json['imsi'] = ""

    if @status == ''
      @temp_json['online'] = true
      @temp_json['last_online'] = @now
    else
      @temp_json['online'] = false
    end
    @temp_json['offline_reason'] = @status

    restriction_scripts_installed?
    check_device_time_settings
    needs_adb_proxy = BrowserStack::ModelDatabase.new(@temp_json['device_name'], version).property(:needs_adb_proxy)
    check_adb_proxy if needs_adb_proxy

    !@temp_json['online']
  end

  def update_sim_details
    @temp_json['sim_details'] = DeviceSIMHelper.sim_info(@device)
  end

  def reset_cleanup_count
    FileUtils.rm("/tmp/#{@device}_cleanup_count")
  rescue Errno::ENOENT
    nil
  end

  def reboot_recovery
    @device_obj.reboot("incorrect boot mode", recovery: true)
  end

  def install_mitm_cert(log_file)
    system(
      "sh #{PATH}/driver_actions.sh install_mitm_cert #{@device}" \
      ">> #{log_file} 2>&1"
    )
  end

  def redo_vpn(log_file)
    UsbVPN.new(@device).redo_vpn(log_file)
  end

  # Returns the number of minutes since the device is offline with this reason
  def offline_reason_time
    offline_reason_time = begin
      Time.strptime(@temp_json['offline_reason_time'], '%Y-%m-%d %H:%M:%S')
    rescue StandardError
      @now
    end
    ((@now - offline_reason_time) / 60).to_i
  end

  def push_device_check_install_to_zombie(offline_reason)
    device_name = @temp_json['device_name']

    `
      #{BS_DIR}/mobile/common/push_to_zombie.rb \
      "android" "android-devicecheck-install" "#{@device}" "#{device_name}" "#{offline_reason}"
    `
  end

  # App UIDs increment each time an app is (re-)installed and UID can go upto 19999 max
  # Uninstalling high UID apps and restarting the device resets the count to max current installed UID
  # This method triggers uninstallation when max_uid > 19000 and uninstalls everything > 11000
  def ensure_uids_available
    should_ensure_uids_available = @device_obj.check_high_uid_apps?
    return unless should_ensure_uids_available

    BrowserStack.logger.info("Ensuring UIDs Available For App Installation")
    high_uid_apps =
      `#{TIMEOUTCMD} 10 #{BrowserStack::ADB} -s #{@device} shell pm list packages -U | sort -nr -t : -k3 | head -10`
    high_uid_apps_list = high_uid_apps.split("\n")
    return if high_uid_apps_list.empty?

    max_uid = high_uid_apps_list[0].match(/uid:(\d+)/)[1].to_i
    return if max_uid < 19000

    high_uid_apps_list.each do |high_uid_app|
      # high_uid_app looks like "package:io.appium.uiautomator2.server.test uid:19996"
      matches = high_uid_app.match(/package:(\S+) uid:(\d+)/)
      bundle_id = matches[1]
      uid = matches[2].to_i
      break if uid < 11000

      if bundle_id.eql?(BROWSERSTACK_DEVICE_OWNER_PACKAGE_NAME)
        @adb.shell("am start -n com.browserstack.deviceowner/.DeviceOwnerActivity --es cmd \"clear-device-owner\"")
      end
      @adb.uninstall(bundle_id)
      BrowserStack.logger.info("Uninstalled #{bundle_id} with UID: #{uid}")
    end
    BrowserStack.logger.info("Uninstalled high UID apps. Rebooting and issuing full cleanup")
    @temp_json['online'] = false
    @temp_json['offline_reason'] = 'Reboot device and install missing apps to reset max UID counter'
    @device_obj.reboot("Rebooting to reset UID counter.")
  end

  def trigger_install_phase(offline_reason, new_install = false) # rubocop:todo Style/OptionalBooleanParameter, Metrics/MethodLength, Metrics/AbcSize, Metrics/CyclomaticComplexity
    @logger_params[:component] = __method__.to_s
    executor = DeviceForkExecutor.new(@device, INSTALL_PHASE[:name], STATE_FILES_DIR, BrowserStack.logger,
                                      @logger_params)
    install_phase_time_bound = INSTALL_PHASE[:timeout] # seconds
    install_phase_log_file = "/var/log/browserstack/#{INSTALL_PHASE[:log_file_prefix]}_#{@device}.log"

    BrowserStack.logger.info("Triggering install phase")
    spawned, metadata = executor.run_once do
      logger_params = { device: @device, component: 'install-phase' }
      install_phase_logger = BrowserStack.init_logger(install_phase_log_file, logger_params)

      case offline_reason
      when 'installing new chrome release'
        chrome_release_installer = ChromeReleaseInstaller.new(@device, install_phase_logger, logger_params)
        begin
          chrome_release_installer.ensure_install
        rescue StandardError => e
          if device_name == "motorola edge 50 fusion"
            BrowserStack.logger.error("Failed to install chrome on moto edge 50 fusion, triggering Full cleanup")
            @full_cleanup_reason = "chrome install failed"
            issue_full_cleanup
          else
            raise e
          end
        end
      when 'outdated browser: edge'
        edge_manager = EdgeManager.new(@device, install_phase_logger, logger_params)
        edge_manager.ensure_install
      when 'outdated browser: firefox'
        firefox_manager = FirefoxManager.new(@device, install_phase_logger, logger_params)
        firefox_manager.send(:ensure_install)
      when 'outdated browser: samsung_browser'
        samsung_browser_manager = SamsungBrowserManager.new(@device, install_phase_logger, logger_params)
        samsung_browser_manager.ensure_install
      when OFFLINE_REASONS[:ui_automation_apps_not_installed]
        ui_automation_apps_manager = UIAutomationAppsManager.new(@device, BrowserStack.logger, @logger_params)
        ui_automation_apps_manager.ensure_install
      when 'outdated browser: uc_browser'
        uc_browser_manager = UCBrowserManager.new(@device, install_phase_logger, logger_params)
        uc_browser_manager.send(:ensure_install)
      when 'media cleanup required'
        manager = MediaManager.new(@device, install_phase_log_file, install_phase_logger)
        manager.clean_media
      when 'outdated gms'
        play_services_manager = PlayServicesManager.new(@device, install_phase_logger, logger_params)
        play_services_manager.send(:ensure_install)
      when OFFLINE_REASONS[:browserstack_app_not_installed]
        browserstack_app_manager = BrowserStackAppManager.new(@device, install_phase_logger, logger_params)
        browserstack_app_manager.send(:ensure_install)
      when OFFLINE_REASONS[:test_orchestrator_app_not_installed]
        test_orchestrator_app_manager = TestOrchestratorAppManager.new(@device, install_phase_logger, logger_params)
        test_orchestrator_app_manager.send(:ensure_install)
      when OFFLINE_REASONS[:huawei_browser_app_not_installed]
        huawei_browser_manager = HuaweiBrowserManager.new(@device, install_phase_logger, logger_params)
        huawei_browser_manager.send(:ensure_install)
      when OFFLINE_REASONS[:test_services_app_not_installed]
        test_services_app_manager = TestServicesAppManager.new(@device, install_phase_logger, logger_params)
        test_services_app_manager.send(:ensure_install)
      when OFFLINE_REASONS[:vpn_app_not_installed]
        vpn_reverse_tether_app_manager = VpnTetherAppManager.new(@device, BrowserStack.logger, logger_params)
        vpn_reverse_tether_app_manager.send(:ensure_install) unless @device_obj.needs_system_vpn_app?
        @adb.uninstall(BrowserStack::GNIREHTET_APP_PACKAGE_NAME)
      when OFFLINE_REASONS[:gnirehtet_permission_not_handled]
        @adb.put_setting('system', 'screen_off_timeout', '86400000')
        system("sh /usr/local/.browserstack/mobile/android/driver_actions.sh ensure_screen_is_unlocked #{@device}")
        UsbVPN.new(@device).ensure_gnirehtet_has_vpn_permissions
        @adb.put_setting('system', 'screen_off_timeout', '0')
      when OFFLINE_REASONS[:gnirehtet_app_not_installed]
        gnirehtet_manager = GnirehtetManager.new(@device, BrowserStack.logger, logger_params)
        gnirehtet_manager.ensure_install
        @adb.uninstall(BrowserStack::VPN_REVERSE_TETHER_APP_PACKAGE_NAME)
      when OFFLINE_REASONS[:gnirehtet_relay_outdated]
        usb_vpn = UsbVPN.new(@device)
        usb_vpn.stop_gnirehtet_relay
        usb_vpn.start_gnirehtet_relay
      when OFFLINE_REASONS[:bstack_reverse_tether_app_not_installed] ||
        OFFLINE_REASONS[:bstack_reverse_tether_forwarder_outdated]
        bstack_reverse_tether_manager = BStackReverseTetherManager.new(@device, BrowserStack.logger, @logger_params)
        bstack_reverse_tether_manager.ensure_install
      when OFFLINE_REASONS[:play_store_not_installed]
        play_store_manager = PlayStoreManager.new(@device, BrowserStack.logger, logger_params)
        play_store_manager.send(:ensure_install)
      when OFFLINE_REASONS[:device_owner_not_installed]
        install_device_owner
      when OFFLINE_REASONS[:browserstack_watcher_not_installed]
        browserstack_watcher_manager = BrowserStackWatcherManager.new(@device, BrowserStack.logger, logger_params)
        BrowserStack.logger.info("Installing browserstack watcher by calling ensure install")
        browserstack_watcher_manager.ensure_install
      when OFFLINE_REASONS[:camera_check_app_not_installed]
        camera_check_manager = CameraCheckManager.new(@device, BrowserStack.logger, logger_params)
        camera_check_manager.send(:ensure_install)
      when OFFLINE_REASONS[:account_present]
        fix_account_present
      when OFFLINE_REASONS[:appium_apps_not_installed]
        appium_app_manager = AppiumAppsManager.new(@device, BrowserStack.logger, logger_params)
        appium_app_manager.ensure_install
      when OFFLINE_REASONS[:google_maps_not_installed]
        googlemaps_manager = GoogleMapsManager.new(@device, BrowserStack.logger, logger_params)
        googlemaps_manager.ensure_install
      when OFFLINE_REASONS[:facebook_app_not_installed]
        facebook_app_manager = FacebookAppManager.new(@device, BrowserStack.logger, logger_params)
        facebook_app_manager.send(:ensure_install)
      when OFFLINE_REASONS[:talkback_app_not_installed]
        talkback_manager = TalkbackManager.new(@device, BrowserStack.logger, logger_params)
        talkback_manager.ensure_install
      when OFFLINE_REASONS[:system_updates_not_blocked]
        SystemUpdateBlockHelper.new(@device, BrowserStack.logger, logger_params).block_updates
      else
        usb_vpn_ip = vpn_ip
        BrowserStack.logger.info("IP for USB is #{usb_vpn_ip}")
        # TODO: new_install is always false, remove this.
        system(
          "sh #{PATH}/driver_actions.sh install #{@device} " \
          "\"#{usb_vpn_ip}\" \"#{new_install}\" " \
          ">> #{install_phase_log_file} 2>&1"
        )

        @pm_list = nil # Burst pm_list cache, since new apps will be installed on the device (Refer #pm_list method)
        unless browserstack_apps_installed?
          BrowserStack.logger.warn("Sending to full cleanup: installation failed.")
          # Calling the method directly instead of setting @full_cleanup_needed
          # Because this part is running inside a fork.
          @full_cleanup_reason = "install_phase_installation_failed"
          issue_full_cleanup
        end
      end

      if @device_obj.uses_bstack_internet_app? && !bstack_reverse_tether_controller.running?(full_check: false)
        bstack_reverse_tether_controller.stop
        bstack_reverse_tether_controller.ensure_running
      elsif !@device_obj.uses_bstack_internet_app? && redo_vpn_required?
        redo_vpn(install_phase_log_file)
      end
      install_mitm_cert(install_phase_log_file) unless @device_obj.mitm_cert_installed?

      os_version ||= @adb.os_version.to_s.to_i
      if os_version >= 7.0
        browser_helper = BrowserHelper.new(@device, os_version, install_phase_logger)
        default_browser_set = browser_helper.browser_default?

        unless default_browser_set
          install_phase_logger.info("Setting default browser")
          browser_helper.check_and_set_browser_as_default(default_browser_set: default_browser_set)
        end
      end

      push_device_check_install_to_zombie(offline_reason)
    end

    @temp_json['online'] = false
    if spawned
      BrowserStack.logger.info("Spawned install-phase process for the device... pid: #{metadata[:pid]}, "\
                               "at: #{metadata[:started_at]}", @logger_params)
      @temp_json["offline_reason"] = "Triggered install-phase due to reason: #{offline_reason}"
    else
      # Device should eventually come online after multiple cycles.
      BrowserStack.logger.info("install-phase is still running on the device... pid: #{metadata[:pid]}, since: "\
                               "#{metadata[:started_at]} (#{Time.now - metadata[:started_at]} seconds)", @logger_params)
      @temp_json['offline_reason'] = "install-phase is still running"

      if Time.now - metadata[:started_at] > install_phase_time_bound
        BrowserStack.logger.warn("install-phase has been running for more than #{install_phase_time_bound} seconds, "\
                                 "killing it assuming it hanged. Triggering full cleanup.", @logger_params)
        executor.reset!
        @full_cleanup_needed = true
        @full_cleanup_reason = "install_phase_stuck"
      end
    end
  end

  def push_kind_and_message_to_zombie(kind, message)
    device_name = @temp_json['device_name']

    # TODO: create a zombie object and use that
    `
      #{BS_DIR}/mobile/common/push_to_zombie.rb \
      "android" "#{kind}" "#{message}" "#{device_name}"
    `
  end

  def delete_file(file)
    BrowserStack.logger.info("Deleting: #{file}")
    FileUtils.rm_f(file)
  end

  def fix_moved_to_offline
    BrowserStack.logger.info("Offline reason on rails Moved to for #{@device}")
    if check_device_state_offline?
      delete_file("#{CUSTOM_CONFIG}/device_static_conf_#{@device}.json")
      delete_file("#{STATE_FILES_DIR}/Moved_to_#{@device}")
      delete_file("#{STATE_FILES_DIR}/device_state_#{@device}")
    end
  end

  def is_moved_to?
    File.exist?("#{STATE_FILES_DIR}/Moved_to_#{@device}") &&
      File.exist?("#{CUSTOM_CONFIG}/device_static_conf_#{@device}.json")
  end

  def fix_offline_reason(offline_reason, _version, cleanup_failure_reason='') # rubocop:todo Metrics/CyclomaticComplexity, Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
    @logger_params[:component] = __method__.to_s
    BrowserStack.logger.info("Trying to fix #{offline_reason}", @logger_params)
    if offline_reason == "outdated browser: chrome"
      # Quick fix to install chrome on the same device check not running full device install as next condition
      BrowserStack.logger.info("outdated chrome, installing correct version", @logger_params)

      # Disable package verifier on device or install will fail
      system("#{BrowserStack::ADB} -s #{@device} shell 'settings put global package_verifier_enable 0; "\
             "settings put global verifier_verify_adb_installs 0;'")

      chrome_release_installer.install

      # Make sure package verifier is enabled
      system("#{BrowserStack::ADB} -s #{@device} shell 'settings put global package_verifier_enable 1; "\
             "settings put global verifier_verify_adb_installs 1;'")
      push_device_check_install_to_zombie(offline_reason)
    elsif offline_reason == 'media cleanup required'
      BrowserStack.logger.info("Cleaning media in install phase")
      trigger_install_phase(offline_reason)
      ui_automation_apps_manager = UIAutomationAppsManager.new(@device, BrowserStack.logger, @logger_params)
      ui_automation_apps_manager.grant_permissions
    elsif offline_reason == 'injected mock media present'
      BrowserStack.logger.info("Attempting to delete mock injected media", @logger_params)
      CameraMediaInjector.cleanup_mock_media(@device)

      if CameraMediaInjector.injected_mock_media_present?(@device)
        BrowserStack.logger.info("Sending to full cleanup: need to delete user's injected mock camera media",
                                 @logger_params)
        @full_cleanup_needed = true
        @full_cleanup_reason = "injected_mock_media"
      end
    elsif offline_reason == 'need tether app as system app'
      BrowserStack.logger.warn("Sending to full cleanup: need vpn_reverse_tether as system app", @logger_params)
      FileUtils.touch("/tmp/install_vpn_as_system_#{@device}")
      @full_cleanup_needed = true
      @full_cleanup_reason = "tether_as_system_app"
    elsif offline_reason == OFFLINE_REASONS[:run_as_root_reboot_required]
      BrowserStack.logger.error('Run as root check failed rebooting to recover', @logger_params)
      zombie.push_logs("run-as-root-failed-reboot", "device: #{@device}")
      @temp_json['offline_reason'] = OFFLINE_REASONS[:run_as_root_rebooted]
      @temp_json['online'] = false
      @device_obj.reboot("reboot_to_fix_run_as_root_failure")
    elsif offline_reason.include?(OFFLINE_REASONS[:battery_level_not_found])
      @full_cleanup_needed = true
      @full_cleanup_reason = "battery_level_not_found"
    elsif offline_reason.include?(OFFLINE_REASONS[:device_owner_not_enabled])
      fix_device_owner_not_enabled
    elsif offline_reason.include?(OFFLINE_REASONS[:device_owner_privileges_manual_fix])
      fix_device_owner_not_enabled
    elsif install_phase_required?
      trigger_install_phase(offline_reason)
    elsif offline_reason == 'incorrect boot mode'
      reboot_recovery
    elsif offline_reason == 'mitm cert not installed'
      cleanup_log_file = "/var/log/browserstack/cleanup_#{@device}.log"
      install_mitm_cert(cleanup_log_file)
      unless @device_obj.mitm_cert_installed?
        BrowserStack.logger.info("mitm cert installation finished, issuing full cleanup", @logger_params)
        issue_full_cleanup
      end
    elsif offline_reason.include?('busybox not found')
      BrowserStack.logger.warn("Sending cleanup: busybox not found", @logger_params)
      issue_cleanup("busybox_not_found")
    elsif offline_reason.include?(OFFLINE_REASONS[:http_proxy_set])
      unset_http_proxy
    elsif offline_reason.include?('internet down') && @device_obj.uses_bstack_internet_app?
      # VPN app is not running properly
      BrowserStack.logger.warn("Trying to recover internet down offline for BRT", @logger_params)
      bstack_reverse_tether_controller.stop
      bstack_reverse_tether_controller.ensure_running
    elsif offline_reason.include?('internet down') && \
          WifiCheck.offline_due_to_no_hostname?(offline_reason) && \
          @device_obj.os_version.to_i >= 11 && \
          (@device_obj.rooted? || (@device_obj.samsung? && @device_obj.os_version.to_i == 12))

      BrowserStack.logger.info("internet is down", @logger_params)
      wifi_check = WifiCheck.new(@device_obj, BrowserStack.logger, @logger_params)
      wifi_check.join_default_network(wifi_ssid, static_config["wifi_password"])
      BrowserStack.logger.info("network changed issuing cleanup", @logger_params)
      issue_cleanup("rejoining BLT network")
    elsif offline_reason.include?('internet down') && \
          WifiCheck.offline_due_to_no_hostname?(offline_reason) && \
          !@device_obj.rooted?

      BrowserStack.logger.info("no hostname, rejoinging wifi", @logger_params)
      rejoin_wifi = RejoinWifi.new(@device_obj.id, BrowserStack.logger)
      rejoin_wifi.rejoin
      BrowserStack.logger.info("rejoin automation finished", @logger_params)
    elsif offline_reason.include?('internet down') || WifiCheck.offline_due_to_this_check?(offline_reason)
      wifi_check = WifiCheck.new(@device_obj, BrowserStack.logger, @logger_params)
      wifi_check.attempt_fix(offline_reason_time)
      unless @device_obj.rooted?
        BrowserStack.logger.info("no hostname, rejoinging wifi", @logger_params)
        rejoin_wifi = RejoinWifi.new(@device_obj.id, BrowserStack.logger)
        rejoin_wifi.rejoin
        BrowserStack.logger.info("rejoin automation finished", @logger_params)
      end
      if @device_obj.os_version.to_i == 12 && @device_obj.rooted?
        wifi_check.join_default_network(wifi_ssid, static_config["wifi_password"])
      end
    elsif offline_reason == 'Browserstack background service not running'
      system("#{BrowserStack::ADB} -s #{@device} shell am force-stop com.android.browserstack")
      system(
        "#{BrowserStack::ADB} -s #{@device} shell "\
        "am start -n \"com.android.browserstack/.main.BrowserStack\" "\
        "--es setWifi \"true\" "\
        "--es ssid \"#{wifi_ssid}\" "\
        "--es pass \"#{static_config['wifi_password']}\""
      )
      system("#{BrowserStack::ADB} -s #{@device} shell 'echo 1 > /sdcard/service_restarted'")
    elsif offline_reason == 'Browserstack service failed to restart'
      BrowserStack.logger.warn("Sending to full cleanup: Browserstack service failed to restart", @logger_params)
      @full_cleanup_needed = true
      @full_cleanup_reason = "browserstack_services_failed_to_start"
    elsif offline_reason == 'SystemUI not running'
      systemui_check = SystemUICheck.new(@device_obj, BrowserStack.logger, @logger_params)
      systemui_check.attempt_fix(offline_reason_time)
    elsif offline_reason.include?('unlock failed') || offline_reason.include?('screen off')
      system("sh #{PATH}/driver_actions.sh device_screen #{@device} fire_unlock")
    elsif offline_reason == 'Restriction scripts not installed'
      restriction_scripts_manager = RestrictionScriptsManager.new(@device, BrowserStack.logger, @logger_params)
      restriction_scripts_manager.ensure_install
    elsif offline_reason == OFFLINE_REASONS[:bstack_vpn_not_running]
      bstack_reverse_tether_controller.stop
      bstack_reverse_tether_controller.ensure_running
    elsif offline_reason.include?('no tun interface found')
      push_kind_and_message_to_zombie("no-tun-found", "no tun interface #{@device}")
      @influxdb_client.track_event('no-tun-found', 'device_check', 'device_metrics', @device, 'true')
      cleanup_log_file = "/var/log/browserstack/cleanup_#{@device}.log"
      redo_vpn(cleanup_log_file)
      if !UsbVPN.new(@device).vpn_tunnel_exists?
        BrowserStack.logger.warn("redovpn failed, sending device to full cleanup", @logger_params)
        push_kind_and_message_to_zombie("redovpn-failed", "no tun interface after retry #{@device}")
        @influxdb_client.track_event('redovpn-failed', 'device_check', 'device_metrics', @device, 'true')
        @full_cleanup_needed = true
        @full_cleanup_reason = "redovpn_failed"
      else
        BrowserStack.logger.info("redovpn successful", @logger_params)
        push_kind_and_message_to_zombie("redovpn-successful", "tun interface available #{@device}")
      end
    elsif offline_reason.include?('Device datetime incorrect')
      BrowserStack.logger.info("Correcting datetime for device #{@device}")
      system("sh #{PATH}/driver_actions.sh set_device_datetime #{@device}" \
             ">> /var/log/browserstack/cleanup_#{@device}.log 2>&1")
      if device_datetime_incorrect?
        system("sh #{PATH}/driver_actions.sh set_device_time_using_host #{@device} >> "\
               "/var/log/browserstack/cleanup_#{@device}.log 2>&1")
      end
      if device_datetime_incorrect? # Do full cleanup if attempted fixes for datetime didn't work
        @full_cleanup_needed = true
        @full_cleanup_reason = "datetime_incorrect"
      end
    elsif offline_reason.include?(OFFLINE_REASONS[:account_present])
      handle_account_present
    elsif offline_reason.include?('cmd_output.txt not found') ||
          cleanup_failure_reason.include?('cmd_output.txt not found')
      BrowserStack.logger.warn("DeviceOwner command failed, sending device to full cleanup", @logger_params)
      push_kind_and_message_to_zombie("deviceowner-command-failure", "cmd_output.txt file not found #{@device}")
      @full_cleanup_needed = true
      @full_cleanup_reason = "deviceowner_command_failed"
    else
      BrowserStack.logger.info("No fix available for #{offline_reason}", @logger_params)
    end
  end

  def timeout_for_version(version)
    # Return big timeout if phone may be old (this includes not knowing version
    # because error getting it)
    return 60 * 7 if version.nil?
    return 60 * 7 unless Gem::Version.correct?(version)
    return 60 * 7 if Gem::Version.create(version) < Gem::Version.new("7")

    60 * 4
  end

  def check_device_state_offline?
    # Deleting device_state_udid in 1440 mins [1d] to get rails_summary once/day
    if is_file_older_than_minutes(File.join(STATE_FILES_DIR, "device_state_#{@device}"), 1440)
      FileUtils.rm_f("#{STATE_FILES_DIR}/device_state_#{@device}")
    end

    begin
      adb_checker = AdbChecker.new(
        machine_ip: @ip,
        conf_devices: devices_json,
        adb_devices: adb_devices,
        static_conf: static_config
      )
    rescue StandardError => e
      BrowserStack.logger.error("Exception while creating adb checker: #{e.message}; #{e.backtrace}")
    end

    devices_response = adb_checker.rails_summary(@device)

    return false if devices_response.empty?

    BrowserStack.logger.info("devices_response #{devices_response[@device]}", @logger_params)
    BrowserStack.logger.info("#{@device} state: #{devices_response[@device]['state']}", @logger_params)

    return true if devices_response[@device]['state'] == "offline"

    false
  end

  #returns a unique range of ports for each device based on its index
  # increasing index by 2 to not conflict with existing chrome_driver_port arg
  # current range is 10, can be increased later if needed
  def get_unique_chromedriver_ports_from_index(index)
    range = 10
    start_port = 10000 + @port + ((index + 2) * range)
    end_port = start_port + 10
    [[start_port, end_port]]
  end

  def start_device_owner_poller(host_ip)
    if device_owner_controller.poller_running?
      BrowserStack.logger.info("DO poller running")
      return
    end
    if !device_owner_manager.install_required? && device_owner_manager.privileges_enabled?
      device_owner_controller.ensure_poller_is_running(host_ip)
    else
      BrowserStack.logger.info("DO poller requirements not met will start in next check if full-filled")
    end
  rescue AndroidToolkit::ADB::ExecutionError => e
    BrowserStack.logger.error("Exception while starting the DO poller: #{e.message}; #{e.backtrace}")
    poller_error_state_file = StateFileHelper.new("start_device_owner_poller_failure_#{@device}")
    if poller_error_state_file.older_than_days?(1)
      poller_error_state_file.reset
      zombie_key_value(
        platform: 'android',
        kind: 'start_poller_failure',
        device: @device,
        os_version: @adb.os_version.to_s.to_i,
        error: e.message
      )
    end
  end

  def run(temp_json = {}) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    start_time = Time.now
    temp_json = {} if temp_json.nil?
    @logger_params[:component] = __method__.to_s

    begin
      timeout(timeout_for_version(temp_json['device_version'])) do
        @full_cleanup_needed = false
        @full_cleanup_reason = "unknown"

        @temp_json = temp_json
        @temp_json['ip'] = @ip
        @temp_json['port'] ||= @port
        @temp_json['debugger_port'] ||= @port.to_s
        @temp_json['dev_tool_port'] ||= "2#{@port}"
        @selenium_port = @temp_json['selenium_port'] ||= "3#{@port}"
        @playwright_port = @temp_json['playwright_port'] ||= "3#{@port.to_i + PLAYWRIGHT_ANDROID_PORT_OFFSET}"
        @ai_proxy_port = @temp_json['ai_proxy_port'] ||= "3#{@port.to_i + AI_PROXY_PORT_OFFSET}"
        @cdp_port = @temp_json['cdp_port'] ||= "3#{@port.to_i + CDP_PORT_OFFSET}"
        @socat_port = @temp_json['socat_port'] ||= "3#{@port.to_i + SOCAT_PORT_OFFSET}"
        @chrome_driver_port = @temp_json['chrome_driver_port'] ||= "1#{@port}"
        @android_bootstrap_port = @selenium_port.to_i + 100
        @temp_json['jackproxy_port'] ||= "4#{@port}"
        @temp_json['jackproxy_https_port'] ||= "5#{@port}"
        @temp_json['device_logger_port'] ||= "2#{@port.to_i + 400}"
        @temp_json['adb_forwarder_port'] ||= calculate_adb_forwarder_port(@port)
        @temp_json['android_bootstrap_port'] ||= @android_bootstrap_port
        @temp_json['zotac_host'] = @hostname
        @temp_json['os'] = 'android'
        @temp_json['region'] = static_config["region"] || "unknown"
        @temp_json['sub_region'] = static_config["sub_region"] || "unknown"
        @temp_json['pool_mask'] = if DeviceSIMHelper.public_sim?(@device)
                                    POOL_MASK[:sim_enabled_device]
                                  elsif AudioInjector.audio_injection_device?(@device)
                                    POOL_MASK[:audio_injection_device]
                                  elsif GooglePay.google_pay_device?(@device)
                                    POOL_MASK[:google_pay_device]
                                  else
                                    static_config['pool_mask']
                                  end
        @temp_json['rails_endpoint'] = static_config.device_rails_endpoint(@device)
        @temp_json['active_session'] = 'false'
        @temp_json["network_logs_port"] ||= (@port.to_i + 100).to_s
        @temp_json["uiinspect_port"] ||= (@port.to_i + 300).to_s
        @temp_json["webview_port"] ||= (@port.to_i + 400).to_s
        @temp_json["device_name"] = 'unknown' if @temp_json["device_name"].nil?

        #chromedriverPorts starts from 18100 and can go upto 18319 for ports in range of 8080 - 8100
        @temp_json['chromedriverPorts'] = @temp_json['chromedriverPorts'] ||
        get_unique_chromedriver_ports_from_index(@port - 8080)
        # 8080 is the start port from device_check#allocate_port
        # NOTE: AA is using selenium_port + 200 for youiengine driver port, please dont use it for anything else
        instrument_and_push_battery_temperature
        snooze_device_managed_notification
        if running_processes?
          @temp_json['online'] = false
          check_in_use_device
          setup_config if old_full_offline_reason.include?("unknown device info")
          @temp_json['cleanup_failure_reason'] = cleanup_failure_reason if need_offline_detail? @temp_json
          if @temp_json['push_battery_stats_to_zombie']
            BrowserStack.logger.info("Battery Instrumentation Push at mid of session : #{@device}")
            BatteryInstrumentor.new(@device, BrowserStack.logger, get_session_id(@device)).push('mid_of_session')
          end
        elsif is_minified_flow
          BrowserStack.logger.info("MINIFIED_CLEANUP on device : #{@device}")
          if is_file_older_than_minutes(File.join(STATE_FILES_DIR, "minimized_cleanup_reserved_#{@device}"),
                                        RESERVED_FLOW_SESSION_MAX_TIME_IN_MINUTES)
            FileUtils.rm_f([File.join(STATE_FILES_DIR, "minimized_cleanup_reserved_#{@device}"),
                            File.join(STATE_FILES_DIR, "preserve_app_state_reserved_#{@device}")])
            @temp_json['online'] = false
            @temp_json['offline_reason'] = 'unclean_reserved_device_needs_releasing'
            @temp_json['cleanup_failure_reason'] = ''
            push_logs_to_zombie(
              @temp_json['offline_reason'],
              { 'device' => @device, 'data' => @temp_json['device_name'] }
            )
          end
        elsif is_dedicated_minified_flow
          BrowserStack.logger.info("DEDICATED_MINIFIED_CLEANUP on device : #{@device}")
          check_and_dismiss_popup
          dedicated_minimized_file = File.join(STATE_FILES_DIR, "dedicated_minimized_cleanup_reserved_#{@device}")
          if is_file_older_than_minutes(dedicated_minimized_file, 48 * 60)
            message = "Triggering dedicated cleanup from dedicated minified cleanup"
            BrowserStack.logger.info(message)
            FileUtils.rm_f(File.join(STATE_FILES_DIR, "dedicated_minimized_cleanup_reserved_#{@device}"))
            FileUtils.touch(File.join(STATE_FILES_DIR, "dedicated_cleanup_#{@device}"))
            FileUtils.touch("#{STATE_FILES_DIR}/needs_reboot_#{@device}")
            zombie_data = { 'device' => @device, 'data' => @temp_json['device_name'] }
            zombie.push_logs("scheduled_android_dedicated_cleanup", "triggered from minified cleanup", zombie_data)
            issue_cleanup(message)
            return
          end
          check_offline_device
        elsif check_reboot_needed
          if was_offline?
            BrowserStack.logger.info("Issuing cleanup due to reboot needed for device #{@device}")
            issue_cleanup("app install failure - cleanup reboot needed")
          else
            BrowserStack.logger.info("Moving offline for the device #{@device}")
            @temp_json['online'] = false
            @temp_json['offline_reason'] = 'app install failure - cleanup reboot needed'
          end
        elsif device_has_dark_screen?
          if was_offline?
            BrowserStack.logger.info("Issuing cleanup due to dark screen bad state for device #{@device}")
            issue_cleanup("dark screen detected - full cleanup needed")
          else
            BrowserStack.logger.info("Moving offline for the device #{@device}")
            @temp_json['online'] = false
            @temp_json['offline_reason'] = 'dark screen detected - full cleanup needed'
          end
        else
          # For new devices plugged, the device_obj wont be set in the first device
          # check, as there is no config file.
          # In the next device check, there should be a config file
          raise DeviceNotInConfig, "@device_obj not ready yet, waiting for next device check" if @device_obj.nil?

          check_device_connected
          check_recover_device_script_running
          setup_config
          ensure_uids_available
          check_and_dismiss_popup
          check_appium(@temp_json['device_name']) # device_name is set on setup_config
          check_default_appium_restart
          check_and_fix_interaction_args(@temp_json['device_name'])
          unblock_device_overheated_offline
          check_offline_device
          update_sim_details
          check_last_url
          check_run_as_root
          push_battery_temperature
          enable_battery_life_optimizer
          ensure_watcher_running

          # Allow browserstack app write settings permission via appops
          grant_write_settings_bs_app

          # whitelist device owner from battery optmisation
          # start device owner poller if not running quietly
          if @device_obj.supports_device_owner? && !@device_obj.skip_polling_service?
            device_owner_manager.whitelist_from_battery_optmisation
            start_device_owner_poller(@ip)
            device_owner_controller.push_off_adb_do_logs
          end

          trigger_install_phase('', true) if devices_json_old[@device].nil?

          fix_moved_to_offline if is_moved_to?

          if !@temp_json['offline_reason'].nil? &&
             @temp_json['offline_reason'] != '' &&
             !devices_json_old[@device].nil? &&
             devices_json_old[@device]['offline_reason'] == @temp_json['offline_reason']
            BrowserStack.logger.info("Device keeps the same offline reason.", @logger_params)
            fix_offline_reason(@temp_json['offline_reason'], @temp_json['device_version'], cleanup_failure_reason)
          end

          issue_full_cleanup if @full_cleanup_needed
        end
        if File.exist?(full_reset_requested_cleanup_file)
          BrowserStack.logger.info("full_reset_requested_cleanup_file found for #{@device}, issuing full cleanup")
          FileUtils.rm_f(full_reset_requested_cleanup_file)
          @full_cleanup_reason = "full_reset_requested"
          issue_full_cleanup
        end

        is_settings_put_working? if @cleanup_function_runner.should_run?("settings_put_working")

        if @temp_json['online']
          if was_offline?
            BrowserStack.logger.info("Device recovered from offline #{old_full_offline_reason}")
            if was_off_adb?
              @replugging_server_command.perform(:cancel)
              zombie.push_logs("device-recovered-from-offline", '', device: @device, data: old_full_offline_reason)
            end
          end
        else
          reset_cleanup_count
          push_logs_to_zombie(
            @temp_json['offline_reason'],
            { 'device' => @device, 'data' => @temp_json['device_name'] }
          )
        end

        if need_offline_detail? @temp_json
          @temp_json['cleanup_failure_reason'] = cleanup_failure_reason
          push_cleanup_failure_to_zombie(@temp_json['cleanup_failure_reason'], { 'device' => @device,
                                                                                 'data' => @temp_json['device_name'] })
        else
          @temp_json.delete('cleanup_failure_reason')
        end
        @temp_json['offline_reason_time'] = if devices_json_old.key?(@device)
                                              update_offline_reason_time(
                                                @temp_json['offline_reason'],
                                                devices_json_old[@device]['offline_reason'],
                                                devices_json_old[@device]['offline_reason_time']
                                              )
                                            else
                                              ''
                                            end
        BrowserStack.logger.info("Conf: #{@temp_json.inspect}", @logger_params)
      end
    rescue ArgumentError => e
      raise e unless e.message =~ /not in db, in any version/

      BrowserStack.logger.error("Device not in DB: #{@device}", @logger_params)
      @temp_json["online"] = false
      @temp_json["offline_reason"] = "device not in db"
    rescue Timeout::Error => e
      begin
        BrowserStack.logger.error("Timeout Trace: #{e.backtrace}", @logger_params)
      rescue StandardError
        nil
      end
      @temp_json = on_timeout(e)
    end

    # Push offline reason to zombie
    begin
      push_offline_reason_to_zombie
    rescue StandardError => e
      BrowserStack.logger.info("Failed to push offline reason to BQ for device #{@device} #{e.message} #{e.backtrace}")
    end

    BrowserStack.logger.info("Device thread #run completed in #{Time.now - start_time}s")
    @temp_json
  end

  # check to make sure run as root is working
  def check_run_as_root
    return unless @device_obj.rooted?

    return if running_processes?

    # Reboot is already scheduled, it will be rebooted in #fix_offline_reason eventually
    return if old_device["offline_reason"] == OFFLINE_REASONS[:run_as_root_reboot_required]

    root_helper = RunAsRootHelper.new(device_id: @device, logger: BrowserStack.logger)
    if root_helper.run_as_root_working?
      BrowserStack.logger.error('Run as root working fine', @logger_params)
      return
    end

    ### run as root is not working

    # Device is not working even after reboot
    if old_device["offline_reason"] == OFFLINE_REASONS[:run_as_root_rebooted]
      BrowserStack.logger.error('Unable to make run_as_root work after reboot, marking for manual fix', @logger_params)
      @temp_json['offline_reason'] = OFFLINE_REASONS[:run_as_root_manual_fix_required]
      @temp_json['online'] = false

      return
    end

    BrowserStack.logger.error('Run as root check failed scheduling reboot to recover', @logger_params)
    @temp_json['offline_reason'] = OFFLINE_REASONS[:run_as_root_reboot_required]
    @temp_json['online'] = false
  end

  def is_settings_put_working?
    # In Oppo devices
    # "Disable Permission Monitoring" setting in Developer Options
    # should be enabled, otherwise `settings put` command fails
    exception = `timeout 3 #{BrowserStack::ADB} -s #{@device} shell settings put system screen_brightness 0 2>&1`.strip
    if exception.include? "Security exception"
      BrowserStack.logger.error('"Disable permission monitoring" setting is not enabled', @logger_params)
      @temp_json['offline_reason'] = 'manual fix required: enable the setting `Disable permission monitoring`'
      @temp_json['online'] = false
      return false
    end
    true
  end

  private

  def full_reset_requested_cleanup_file
    File.join(STATE_FILES_DIR, "full_reset_requested_cleanup_#{@device}")
  end

  def is_minified_flow
    File.exist?(File.join(STATE_FILES_DIR, "minimized_cleanup_reserved_#{@device}"))
  end

  def is_dedicated_minified_flow
    File.exist?(File.join(STATE_FILES_DIR, "dedicated_minimized_cleanup_reserved_#{@device}"))
  end

  def is_file_older_than_minutes(file_path, minutes)
    !File.exist?(file_path) || (File.mtime(file_path) < (Time.now - (60 * minutes)))
  end

  def account_removal_manual_fix_file
    File.join(STATE_FILES_DIR, "account_removal_manual_fix_#{@device}")
  end

  def usb_crash_report
    @usb_crash_report ||= UsbCrashReport.new
  end

  def publish_usb_crash_report
    zombie.push_logs("mobile-usb-controller-down", usb_crash_report.report_summary) if usb_crash_report.needs_rebind?
  end

  def device_name
    if @temp_json.nil? || @temp_json['device_name'].nil? || @temp_json['device_name'].empty?
      adb_get_device_name
    else
      @temp_json['device_name']
    end
  end

  def check_reboot_needed
    BrowserStack.logger.info("Checking if reboot file is there needs_reboot_for_install_failures_#{@device}")
    if File.exist?(File.join(STATE_FILES_DIR, "needs_reboot_for_install_failures_#{@device}"))
      BrowserStack.logger.info("Reboot file exists")
    end
  end

  def device_has_dark_screen?
    if @device_obj.model == 'SM-G973F'
      BrowserStack.logger.info("Checking if the device has dark screen")
      current_focus = `#{TIMEOUTCMD} 10 #{BrowserStack::ADB} -s #{@device} shell dumpsys window |
      grep mCurrentFocus |
      grep null -c`
      BrowserStack.logger.info("current_focus null count is #{current_focus}")
      return current_focus.strip.to_i > 0
    end
    false
  end

  def adb_get_device_name
    device_name = @adb.model
    device_name.empty? ? 'unknown' : device_name
  end

  def push_offline_reason_to_zombie
    return if @temp_json['online'] || !@temp_json['push_offline_to_zombie']

    offline_reason = @temp_json['offline_reason']

    if ["Device under cleaning", "manual cleanup"].include? offline_reason
      return if @temp_json['cleanup_failure_reason'].nil?

      offline_reason = @temp_json['cleanup_failure_reason']
    end

    offline_reasons_to_exclude = ["Moved"]
    return if offline_reasons_to_exclude.any? { |word| offline_reason.include?(word) }

    trimmed_offline_reason = offline_reason.sub(/low battery.*/, "low battery").sub(/timeout.*/, "timeout")
    zombie_data = { 'device' => @device, 'data' => @temp_json['device_name'] }

    BrowserStack.logger.info("Pushing offline reason: #{trimmed_offline_reason} to zombie")
    zombie.push_logs("mobile_offline_reason", trimmed_offline_reason, zombie_data)
  end

  def wifi_ssid
    wifi_experiment_enabled?(@adb.model, static_config["sub_region"]) ? "BLT iOS" : static_config["ssid"]
  end

  def vendor_build_date
    begin
      vendor_build_date = @adb.getprop("ro.vendor.build.date")
    rescue StandardError => e
      vendor_build_date = nil # if prop is not present return nil
    end
    vendor_build_date
  end

  def valid_device_info?(value)
    value && !value.strip.empty? && value.downcase != 'unknown'
  end

  def device_resolution
    begin
      resolution_output = @adb.shell("wm size")
      resolution_value = resolution_output[/Physical size:\s*(\d+x\d+)/, 1] # extract resolution string like "1080x2400"
      BrowserStack.logger.info("Detected resolution: #{resolution_value}")
    rescue StandardError => e
      BrowserStack.logger.error("Error fetching resolution: #{e.message}")
      resolution_value = nil
    end
    valid_device_info?(resolution_value) ? resolution_value : nil
  end

  def device_ram
    begin
      ram_output = @adb.shell("cat /proc/meminfo | grep MemTotal")
      mem_kb = ram_output[/\d+/]&.to_i
      ram_value = nil
      if mem_kb
        thresholds = [
          [18_874_368, "24GB"],
          [16_777_216, "18GB"],
          [12_582_912, "16GB"],
          [8_388_608,  "12GB"],
          [6_291_456,  "8GB"],
          [4_194_304,  "6GB"],
          [2_097_152,  "4GB"],
          [1_048_576,  "2GB"]
        ]
        ram_value = thresholds.find { |threshold, _| mem_kb > threshold }&.last || "unknown"
      end
      BrowserStack.logger.info("Detected RAM: #{ram_value}")
    rescue StandardError => e
      BrowserStack.logger.error("Error fetching RAM: #{e.message}")
      ram_value = nil
    end
    valid_device_info?(ram_value) ? ram_value : nil
  end

  def device_storage
    begin
      storage_output = @adb.shell("df || true")
      lines = storage_output.lines.reject { |l| l.strip.empty? || l.include?("tmpfs") || l.include?("loop") }
      data_line = lines.find { |line| line.split.last == "/data" }
      data_line ||= lines.find { |line| line.split.last =~ %r{^/mnt/shell/enc_emulated$|^/storage/emulated$} }
      if data_line.nil?
        excluded_mounts = %w[
          /system /system_root /product /vendor /cache /metadata /efs /boot /mnt
          /odm /dev /proc /sys /acct /config
        ]
        data_lines = lines.select do |line|
          mount = line.split.last
          !excluded_mounts.include?(mount) &&
            mount.start_with?("/") && line.split[1].to_i > 0
        end
        data_line = data_lines.max_by { |line| line.split[1].to_i }
      end
      storage_value = nil
      if data_line
        size_kb = data_line.split[1].to_i
        thresholds = [
          [536_870_912, "1TB"],
          [268_435_456, "512GB"],
          [134_217_728, "256GB"],
          [67_108_864,  "128GB"],
          [33_554_432,  "64GB"],
          [16_777_216,  "32GB"]
        ]
        storage_value = thresholds.find { |threshold, _| size_kb >= threshold }&.last || "unknown"
        BrowserStack.logger.info("Detected Storage: #{storage_value}")
      end
    rescue StandardError => e
      BrowserStack.logger.error("Error fetching Storage: #{e.message}")
      storage_value = nil
    end
    valid_device_info?(storage_value) ? storage_value : nil
  end

  def device_chipset
    begin
      cmd = <<~SHELL.strip
        for prop in ro.hardware.chipname ro.soc.model ro.board.platform ro.hardware ro.product.board; do
          val=$(getprop $prop);
          if [ ! -z "$val" ]; then
            echo $val;
            exit;
          fi;
        done;
        cat /proc/cpuinfo | grep -i "hardware" | head -1 | cut -d ":" -f2 | xargs
      SHELL
      chipset_output = @adb.shell(cmd)
      chipset_value = chipset_output&.strip
      BrowserStack.logger.info("Detected chipset: #{chipset_value}")
    rescue StandardError => e
      BrowserStack.logger.error("Error fetching chipset: #{e.message}")
      chipset_value = nil
    end
    valid_device_info?(chipset_value) ? chipset_value : nil
  end

  def log(level, msg)
    BrowserStack.logger.send(level.to_sym, msg, @logger_params)
  end

  def snooze_device_managed_notification
    before_snooze_notification_list = @adb.shell("cmd notification list")
    if before_snooze_notification_list.include?('0|android|1002|null|1000')
      notification_snooze_output = @adb.shell("cmd notification snooze --for 86400000 \"0|android|1002|null|1000\"")
      BrowserStack.logger.info "Device is managed Notification Snooze Output: #{notification_snooze_output}"
    else
      BrowserStack.logger.info "Device is managed Notification Does Not Exist"
    end
  rescue Exception => e
    BrowserStack.logger.error "Exception in snooze_device_managed_notification : #{e.message}"
  end
end
