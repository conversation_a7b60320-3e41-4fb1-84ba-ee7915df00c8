#!/usr/bin/env bash

export LOG_FILE="/var/log/browserstack/live_${DEVICEID}.log"
export WEBRTC_PATH="/usr/local/.browserstack/webrtc"
export WEBRTC_APK="$WEBRTC_PATH/RTCActivity.apk"
export WEBRTC2_APK="$WEBRTC_PATH/RTCActivity2.apk"
export HELPERS="/usr/local/.browserstack/mobile/android/helpers"
export COMMON="/usr/local/.browserstack/mobile/common"
export DATA_LOCAL_TMP="/data/local/tmp"
export FIREFOX_START_STATUS="/data/local/tmp/go_firefox"
export CHROME_START_STATUS="/data/local/tmp/go_chrome"
export UC_START_STATUS="/data/local/tmp/go_uc"
export MOBILE_RTC_CONFIG="/sdcard/rtc_service"
export GET_URL="/usr/local/.browserstack/mobile/android/live/scripts/getLastUrl.sh"
export INTERACTION_CONFIG_FILE="/usr/local/.browserstack/mobile/android/live/interaction_server/device_config.json"
export DEBUG_PORT="localabstract:chrome_devtools_remote"
export PATH_SOCKET="/usr/local/.browserstack/sockets"

# TODO: Remove this, this is not used
export CHECK_STATUSBAR="$DATA_LOCAL_TMP/check_status_bar.sh"
export TEMP_STATUSBAR_FILE="$DATA_LOCAL_TMP/check_statusbar"
export PACTION="/usr/local/.browserstack/privoxy"
export BROWSER_MODE_FILES="/data/local/tmp/mode.*"
export IP=`cat /usr/local/.browserstack/whatsmyip`
export MOBILE_PACKAGE_FILE="/sdcard/app_package_name"
export BUSYBOX="/data/local/tmp/busybox"
export STATE_FILES_DIR="/usr/local/.browserstack/state_files"
export DEVICE_LOGGER_SCRIPT="/usr/local/.browserstack/mobile/android/device_logger/device_logger.rb"

codename=$($ADB -s $DEVICEID shell getprop ro.build.version.release_or_codename | tr -d '\r\n')
firmware=$($ADB -s $DEVICEID shell getprop ro.build.version.incremental | tr -d '\r\n')
if [[ "$codename" == "VanillaIceCream" ]]; then
  export OS_VERSION="15"
elif [[ "$firmware" == "12787338" ]]; then
  export OS_VERSION="16"
else
  export OS_VERSION=$(adb -s $DEVICEID shell getprop ro.build.version.release | tr -d '\r\n')
fi

source /usr/local/.browserstack/mobile/android/common.sh
source /usr/local/.browserstack/mobile/android/helpers/version_comparison.sh

if [ -f /usr/local/.browserstack/dev_hostname ]; then
  export IP=`cat /usr/local/.browserstack/dev_hostname`;
fi


FREE_SPACE_PATH="/sdcard/free-space"
CLEAN_UP_THRESHOLD=$((1024 * 1024))
SESSION_START_DIR="$STATE_FILES_DIR/session_start"
ENABLE_AUDIO="false"
AUTOMATE_STREAMING="false" # gets set to true when starting streaming/interactive session during automate session
LOCALE_APP_LIVE="false"
USE_RTC_V2="false"

declare -A bash_start_instrumentation

mark_event_start() {
  event_name=$1
  bash_start_instrumentation[$event_name]=$(date +%s%N | cut -b1-13)
}

mark_event_end() {
  event_name=$1
  current_timestamp=$(date +%s%N | cut -b1-13)
  bash_start_instrumentation[$event_name]=$((current_timestamp-${bash_start_instrumentation[$event_name]}))
}

log_bash_instrumentation() {
  bash_start_instrumentation['start_method']=$1
  json_str="device_id:$DEVICEID,"
  for event_name in "${!bash_start_instrumentation[@]}"; do
    echo "LSE bash_start_instrumentation: $event_name - ${bash_start_instrumentation[$event_name]}"
    json_str+="$event_name:${bash_start_instrumentation[$event_name]},"
  done
  $COMMON/push_to_cls.rb $CONFIG/rtc_service_$DEVICEID "bash_start_instrumentation" "" "$json_str" &
}

reset_bash_instrumentation() {
  bash_start_instrumentation=()
}

#moved from live_actions.sh

do_pre_start() {
  #stop_inputinjector_and_screenshot
  if [ "$2" == "true" ]; then
    AUTOMATE_STREAMING="true"
  fi
  if [ "$4" == "true" ]; then
    USE_RTC_APP_AUDIO="true"
  else
    USE_RTC_APP_AUDIO="false"
  fi
  if [ "$6" == "v2" ]; then
    USE_RTC_V2="true"
  else
    USE_RTC_V2="false"
  fi
  if [ "$7" == "true" ]; then
    ENABLE_ROTATION_USING_APP_ORIENTATION="true"
  else
    ENABLE_ROTATION_USING_APP_ORIENTATION="false"
  fi
  if [ "$8" == "true" ]; then
    ENABLE_BS_IME="true"
  else
    ENABLE_BS_IME="false"
  fi
  if [ "${12}" == "true" ]; then
    HANDLE_MP_DURING_ROTATION="true"
  else
    HANDLE_MP_DURING_ROTATION="false"
  fi
  if [ ! -f $CONFIG/calculated_args_$DEVICEID ] || [ ! -f $CONFIG/debug_screenshot_$DEVICEID ]; then
    calculate_streaming_interaction_params
  fi
  if [ ! -f $CONFIG/calculated_interaction_args_$DEVICEID ]; then
    calculate_interaction_args
  fi
  # ENABLE_AUDIO flag is used in live for handling media projection pop up for video streaming
  if [ "$1" == "true" ]; then
    ENABLE_AUDIO="true"
  fi
  if [ "$3" != "false" ]; then
    LOCALE_APP_LIVE="$3"
  fi
  # synchronous start for audio injection , sync webrtc and google pay flag from railsApp
  if [ "$5" == "true" ] || [ "${10}" == "true" ] || [ "${11}" == "true" ]; then
    SYNC_WEBRTC="true"
  fi

  start_touch_server_and_screenshot_and_inputinjector &
  start_webrtc_app "$1"

  if [ "$5" == "true" ]; then
    enable_audio_injection
  fi

  if jq -e --arg id "$DEVICEID" 'has($id)' $BS_DIR/mobile/config/custom_devices/android_sim_devices > /dev/null; then
    block_sim_pages
  fi

  if [ "$9" == "true" ]; then
    enable_sim
  fi

  if [ "${11}" == "true" ]; then
    genre=$(jq -r .genre $STATE_FILES_DIR/session_"$DEVICEID")
    session_id=$(jq -r .live_session_id $STATE_FILES_DIR/session_"$DEVICEID")
    $BUNDLE exec ruby "$GOOGLE_PAY_HELPER" "disable_google_play_services" "$DEVICEID" "$session_id" "live"
  fi
}

# Used for start and update events to device-logger
start_device_logger() {
  run_ruby_code $DEVICE_LOGGER_SCRIPT "$DEVICEID" "start"
}

start_touch_server_and_screenshot_and_inputinjector() {
  cls_log_message "starting_uiautomator"
  INTERACTION_ARGS=$(cat $CONFIG/calculated_interaction_args_$DEVICEID)


  if [[ -z $device_model ]]; then
      warn "No device_model. Trying to fetch..."
      get_device_model
  fi

  INTERACTION_ARGS=$(cat $CONFIG/calculated_interaction_args_$DEVICEID)
  STREAMING_ARGS=$(cat $CONFIG/streaming_params_$DEVICEID)

  if [ -f "$INTERACTION_CONFIG_FILE" ] && jq -e ".deviceConfigurations[\"$device_model\"]" "$INTERACTION_CONFIG_FILE" > /dev/null; then
    streaming_width=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_width" "$INTERACTION_CONFIG_FILE")
    streaming_height=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_height" "$INTERACTION_CONFIG_FILE")
    max_width=$(jq -r ".deviceConfigurations[\"$device_model\"].max_width" "$INTERACTION_CONFIG_FILE")
    max_height=$(jq -r ".deviceConfigurations[\"$device_model\"].max_height" "$INTERACTION_CONFIG_FILE")
  else
    echo "$device_model not found in $INTERACTION_CONFIG_FILE or file absent. Falling back to STREAMING_ARGS."
    streaming_width=$(echo "$STREAMING_ARGS" | grep -o 'streaming_width=[^ ]*' | cut -d '=' -f2)
    streaming_height=$(echo "$STREAMING_ARGS" | grep -o 'streaming_height=[^ ]*' | cut -d '=' -f2)
    max_width=$(echo "$STREAMING_ARGS" | grep -o 'max_width=[^ ]*' | cut -d '=' -f2)
    max_height=$(echo "$STREAMING_ARGS" | grep -o 'max_height=[^ ]*' | cut -d '=' -f2)
  fi

  width_height="$streaming_width $streaming_height"
  touch_screen_size="$max_width $max_height"

  if (device_is_a bsrun_device || [[ "$device_model"  == "E6653" ]]) && [[ "$device_model" != "Pixel 5" ]] && [[ "$device_model" != "Pixel 6" ]] && ! device_is_a uses_hidden_api_interaction_server; then
    echo "Using interactions_server"
    if needs_to_run_interactions_server_with_bsrun; then
        run_as_root "/data/local/tmp/interactions_server $INTERACTION_ARGS &"
        interactions_server_command=""
    else
        interactions_server_command="sh start_binary.sh $DATA_LOCAL_TMP/interactions_server \"$INTERACTION_ARGS\" &"
    fi
  else
    # new interactions-server is based on scrcpy implementation
    # it is downloaded on mini in device check and pushed to device during cleanup
    # download is taken care of by bash code and push is taken care of by ruby code

    # if interactions-server is updated, the newer version is not used unless it is present on device
    # the previous interactions-server is used in such cases since it is already present on device

    # updated version of interactions-server
    interaction_server_version="1.6.0"
    interaction_server_mini_path="$WEBRTC_PATH/interactions-server-$interaction_server_version"

    # if updated version of interactions-server is not present on device
    if [ -z "`$ADB -s $DEVICEID shell ls $DATA_LOCAL_TMP | grep interactions-server-$interaction_server_version`" ]; then
      # and if updated version of interactions-server is present on mini, push it to device
      # else use previous version of interactions-server
      if [ -f $interaction_server_mini_path ]; then
        $ADB -s $DEVICEID push $interaction_server_mini_path $DATA_LOCAL_TMP
      else
        # previous version of interactions-server
        interaction_server_version="1.5.0"
      fi
    fi
    echo "Using interactions-server-$interaction_server_version"
    interactions_server_command="CLASSPATH=/data/local/tmp/interactions-server-$interaction_server_version app_process / com.browserstack.interactionsserver.Server $touch_screen_size &"
  fi

  # For Android 12 we are using a new streaming solution based on minicap's Kotlin app.
  if major_vers_eq "$OS_VERSION" "12"; then
    width_height="${streaming_width}x${streaming_height}"
    real_size=$(adb -s $DEVICEID shell 'wm size' | grep 'Physical size' | awk -F ': ' '{ print $2 }' | tr -d "\n\r")
    adb -s $DEVICEID shell <<__EOF &
      cd $DATA_LOCAL_TMP
      CLASSPATH=$DATA_LOCAL_TMP/minicap-debug.apk app_process /system/bin io.devicefarmer.minicap.Main -P $real_size@$width_height/0 &
      $interactions_server_command
      cd /
__EOF
  else
    adb -s $DEVICEID shell <<__EOF &
      cd $DATA_LOCAL_TMP
      sh start_binary.sh $DATA_LOCAL_TMP/bs_screencap "$width_height live" &
      $interactions_server_command
      cd /
__EOF
  fi

  # start UI automator for all version except 10
  # we'll start UI automator for v10 after handling MediaProjection Pop up
  if major_vers_eq "$OS_VERSION" "10" && [ "$ENABLE_AUDIO" == "true" ]; then
    echo "starting UI automator after handling MediaProjection Pop up"
  else

    if [ "$AUTOMATE_STREAMING" == "false" ] || [ "$USE_RTC_V2" == "true" ]; then
      start_uiautomator
    fi
  fi
}

stop_inputinjector_and_screenshot() {
	grep_str="bs_screencap\|interactions_server\|uiautomator"
	if [ -z $1 ]
	then
		grep_str="bs_screencap\|uiautomator"
	fi
	pids=`adb -s $DEVICEID shell ps | grep $grep_str | awk '{print $2}'`
	while true
	do
		if [ -z "$pids" ]
		then
			echo "binaries stopped, killing poll."
			break
		fi
		echo $pids | xargs adb -s $DEVICEID shell kill -2
		sleep 0.5
		pids=`adb -s $DEVICEID shell ps | grep $grep_str | awk '{print $2}'`
	done
}

calculate_stride(){
  bits_per_pixel=$((8*$2))
  res=`echo "($1 + $bits_per_pixel - 1)/$bits_per_pixel" | bc` # Ceil(width/bits_per_pixel)
  if [ $(($res % 2)) -eq 0 ]; then
    echo $(($res * $bits_per_pixel))
  else
    echo $(( ($res + 1) * $bits_per_pixel))
  fi
}

calculate_streaming_interaction_params(){
  get_os_version
  if vers_gte "$OS_VERSION" "12"; then
    get_device_model
    bytes_per_pixel=4 # Arbitrary, try to change in case of bad rendering or start errors
    WIDTH=$(calculate_stride "$WIDTH" "$bytes_per_pixel")
    device_resolution_str=( $(adb -s $DEVICEID shell wm size | awk '{ print $3 }' ) )
  else
    WIDTH=`adb -s $DEVICEID shell "$DATA_LOCAL_TMP/bs_screencap $WIDTH $HEIGHT getstride 2>/dev/null"`

    if vers_gte "$OS_VERSION" "9"; then
      device_resolution_str=( $(adb -s $DEVICEID shell dumpsys window | grep "mSystem=" | awk -F "]" '{print $2}' | tr -d "[" | tr "," "x" ) )
    else
      device_resolution_str=( $(adb -s $DEVICEID shell dumpsys window | grep "mUnrestrictedScreen" | head -n 1 | tr -d ' ' | awk -F ')' '{print $2}' | tr -d '\r' | tr -d '\n') )
    fi
  fi
  IFS='x' read -a device_resolution <<< "$device_resolution_str"
  aspect_ratio=`bc <<< "scale = 10; ${device_resolution[0]} / ${device_resolution[1]}"`
  HEIGHT=`bc <<< "scale = 0; $WIDTH / $aspect_ratio"`

  max_xy=( $(adb -s $DEVICEID shell getevent -l -i $touchfd | grep "ABS_MT_POSITION_X\|ABS_MT_POSITION_Y" | awk -F 'max ' '{print $2}'| awk -F ',' '{print $1}') )
  max_x=`echo ${max_xy[0]}`
  max_y=`echo ${max_xy[1]}`

  if [ ! -z $WIDTH ]; then
    echo "streaming_width=$WIDTH streaming_height=$HEIGHT max_width=$max_x max_height=$max_y" > $CONFIG/streaming_params_$DEVICEID
  else
    echo "frame calculations failed"
  fi
}

calculate_interaction_args() {
  touchfd=$(adb -s $DEVICEID shell "getevent -S" | \
   grep -B1 'touchscreen\|touch_dev\|amazon_touch\|synaptics\|atmel_mxt_ts\|synaptics_rmi4_i2c\|synaptics_dsxv26\|clearpad\|fts\|NVTCapacitiveTouchScreen\|\<touchpanel\>' | \
   head -1 | awk -F ':' '{print $2}' | tr -d '\r\n ')
  if [ -z touchfd ]
  then
    touchfd="error"
  fi
  echo -n "$touchfd $INTERACTION_ARGS" > $CONFIG/calculated_interaction_args_$DEVICEID
}

set_vars_for_cleanup() {
  [ -f $STATE_FILES_DIR/cleanupdone_$DEVICEID ] && IS_CLEANUP=true || IS_CLEANUP=false
  JSON_STR="device_id:$DEVICEID,is_cleanup:$IS_CLEANUP"

  if [ "$IS_CLEANUP" == "true" ]; then
    ENABLE_AUDIO=true
    LOCALE_APP_LIVE=false
    AUTOMATE_STREAMING=true
  fi
}

handle_media_projection_popup_via_keyevent() {
  handle_attempts=0
  is_popup_visible=false
  is_popup_closed=false

  device_model=$(adb -s $DEVICEID shell getprop ro.product.model)
  while true
  do
    echo "Inside while loop of handle_media_projection_popup_via_keyevent function || handle_attempts: $handle_attempts, is_popup_visible: $is_popup_visible, is_popup_closed: $is_popup_closed" >&2
    if [[ "$handle_attempts" -ge 40 ]]; then
      break;
    fi

    perm_window=$(adb -s $DEVICEID shell dumpsys window | grep -E 'mCurrentFocus|mFocusedApp|ActivityRecord' | grep "MediaProjectionPermissionActivity")
    handle_attempts=$((handle_attempts + 1))

    if [ -z "$perm_window" ]; then
      sleep 1
    else
      # needed in case "start now" button is not visible in popup in landscape mode
      is_popup_visible=true

      if [[ "$device_model" == SM-S93* ]]; then
        echo "Handling Media Projection Popup for Samsung S25 series"
        # Handling Media Projection Popup for Samsung S25 series
        $(adb -s $DEVICEID shell input roll 0 10) # scroll focus window vertically downwards
        sleep 0.1
        $(adb -s $DEVICEID shell input keyevent 19) # Up arrow key to navigate to drop down menu
        sleep 0.1
        $(adb -s $DEVICEID shell input keyevent 23) # Selecting drop down menu
        sleep 0.1
        $(adb -s $DEVICEID shell input keyevent 20) # Down arrow key to navigate "Entire screen"
        sleep 0.1
        $(adb -s $DEVICEID shell input keyevent 23) # Selecting "Entire screen"
        sleep 0.1
        $(adb -s $DEVICEID shell input keyevent 20) # Down arrow key to navigate "Start now"
        sleep 0.1
        $(adb -s $DEVICEID shell input keyevent 22) # Navigating to "Start now" button
        sleep 0.1     
        $(adb -s $DEVICEID shell input keyevent 62) # Pressing "Start now" button
        sleep 0.1
      else
        echo "Handling Media Projection Popup for S24 series"
        # Handling Media Projection Popup for other devices
        $(adb -s $DEVICEID shell input roll -10 0) # scroll focus window horizontally upwards
        $(adb -s $DEVICEID shell input keyevent 22) # navigate to start now button
        $(adb -s $DEVICEID shell input keyevent 62) # press the start now button
      fi
      sleep 0.5

      perm_window=$(adb -s $DEVICEID shell dumpsys window | grep -E 'mCurrentFocus|mFocusedApp|ActivityRecord' | grep "MediaProjectionPermissionActivity")

      if [ -n "$perm_window" ]; then
        # needed in case "start now" button is not visible in popup in portrait mode
        $(adb -s $DEVICEID shell input roll 0 10) # scroll focus window vertically upwards
        $(adb -s $DEVICEID shell input keyevent 22) # navigate to start now button
        $(adb -s $DEVICEID shell input keyevent 62) # press the start now button
        sleep 0.5

        perm_window=$(adb -s $DEVICEID shell dumpsys window | grep -E 'mCurrentFocus|mFocusedApp|ActivityRecord' | grep "MediaProjectionPermissionActivity")
        if [ -z "$perm_window" ]; then
          is_popup_closed=true
        fi
      else
        is_popup_closed=true
      fi

      break;
    fi
  done

  echo "$handle_attempts $is_popup_visible $is_popup_closed"
}

handle_media_projection_popup() {
  # set variables to skip certain steps when called via /cleanup
  set_vars_for_cleanup
  echo "[PRE_START] post set_vars_for_cleanup JSON_STR $JSON_STR"

  if vers_gte "$OS_VERSION" "10" && [ "$ENABLE_AUDIO" == "true" ]; then
    handle_attempts=0
    while true
    do
      # If retries > 80 i.e. 8 seconds, break
      # timeout is on the greater side because unlocking the device doesn't let the pop up show.
      if [[ "$handle_attempts" -ge 80 ]]; then
        $COMMON/push_to_cls.rb $CONFIG/rtc_service_$DEVICEID "MediaProjectionTimeout" "" "$JSON_STR"
        break
      fi
      handle_attempts=$((handle_attempts + 1))

      perm_window=$(adb -s $DEVICEID shell dumpsys window | grep -E 'mCurrentFocus|mFocusedApp|ActivityRecord' | grep "MediaProjectionPermissionActivity")
      if [ -z "$perm_window" ]; then
        echo "Projection window not found, waiting"
      else
        echo "Projection window found $LOCALE_APP_LIVE"

        if [[ "$OS_VERSION" -ge 15 || "$device_model" == "BON-AL00" || "$device_model" == "Pixel 9" || "$device_model" == "Pixel 9 Pro XL" ]]; then
          if [[ "$device_model" == "Pixel 6a" ]]; then
            echo "Executing MediaProjectionHandlerTest for Pixel 6a"
            popup_handler="am instrument -w -r -e debug false -e class 'com.browserstack.uiautomation.MediaProjectionHandlerTest' com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner"
            output=$(adb -s $DEVICEID shell $popup_handler 2>/dev/null)
          else
            mp_popup_coords=$(jq -r .media_projection_popup_coords $STATE_FILES_DIR/session_"$DEVICEID")
            if [ "$mp_popup_coords" != "null" ] && [ -n "$mp_popup_coords" ]; then
              echo "Using coordinates from session file to handle media projection popup: $mp_popup_coords"
            else
              echo "Coordinates not found in session file, handle via json coordinates.."
              mp_popup_coords="null"
            fi
            $BUNDLE exec ruby "$POPUP_HANDLER_HELPER" "handle_media_projection_popup_via_coordinates" "$DEVICEID" "$OS_VERSION" "$session_id" "$mp_popup_coords"
          fi

        elif major_vers_eq "$OS_VERSION" "10"; then
          popup_handler="uiautomator runtest PopupHandlerAutomation.jar -c com.browserstack.popupHandler.MainAutomation"
          output=$(adb -s $DEVICEID shell $popup_handler)
          if ["$AUTOMATE_STREAMING" == "false" ]; then
            start_uiautomator
          fi
        elif vers_gte "$OS_VERSION" "11"; then
          popup_handler="am instrument -w -r -e debug false -e class 'com.browserstack.uiautomation.PopUpHandlerTest' com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner"
          output=$(adb -s $DEVICEID shell $popup_handler)
        fi

        # changing locale for app live
        # skipped same functionality in ruby code else this media pop-up fails to be accepted
        change_locale

        time_taken=$(echo $output | grep -oe "Time:.*OK" | cut -d " " -f 2)
        $COMMON/push_to_cls.rb $CONFIG/rtc_service_$DEVICEID "MediaProjectionHandledIn" "$time_taken" "$JSON_STR"
        $COMMON/push_to_cls.rb $CONFIG/rtc_service_$DEVICEID "MediaProjectionHandleAttempts" "$handle_attempts" "$JSON_STR"
        break
      fi
      sleep 0.1
    done
  fi
}

start_webrtc_app() {
	cat "$CONFIG/rtc_service_$DEVICEID"
	cls_log_message "rtc_app_starting"

  if [[ -z $device_model ]]; then
      warn "No device_model. Trying to fetch..."
      get_device_model
  fi


  STREAMING_ARGS=$(cat $CONFIG/streaming_params_$DEVICEID)

  if [ -f "$INTERACTION_CONFIG_FILE" ] && jq -e ".deviceConfigurations[\"$device_model\"]" "$INTERACTION_CONFIG_FILE" > /dev/null; then
    streaming_width=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_width" "$INTERACTION_CONFIG_FILE")
    streaming_height=$(jq -r ".deviceConfigurations[\"$device_model\"].streaming_height" "$INTERACTION_CONFIG_FILE")
    max_width=$(jq -r ".deviceConfigurations[\"$device_model\"].max_width" "$INTERACTION_CONFIG_FILE")
    max_height=$(jq -r ".deviceConfigurations[\"$device_model\"].max_height" "$INTERACTION_CONFIG_FILE")
  else
    echo "$device_model not found in $INTERACTION_CONFIG_FILE or file absent. Falling back to STREAMING_ARGS."
    streaming_width=$(echo "$STREAMING_ARGS" | grep -o 'streaming_width=[^ ]*' | cut -d '=' -f2)
    streaming_height=$(echo "$STREAMING_ARGS" | grep -o 'streaming_height=[^ ]*' | cut -d '=' -f2)
    max_width=$(echo "$STREAMING_ARGS" | grep -o 'max_width=[^ ]*' | cut -d '=' -f2)
    max_height=$(echo "$STREAMING_ARGS" | grep -o 'max_height=[^ ]*' | cut -d '=' -f2)
  fi

  calculated_args="--ei width $streaming_width --ei height $streaming_height --ei max_x $max_width --ei max_y $max_height"
  # running this adb command in background, as statusbar check is blocking (inf loop)
  start_network_service="am startservice --user 0 -n com.android.browserstack/.services.NetworkService --es task "rtc_cls_init" --es ip \"$IP\""
  start_rtc_service="am startservice --user 0 -n $(rtc_app_use_package)/.RTCService --es action \"start\" --es ip \"$IP\" $calculated_args --es genre \"$GENRE\" --es enableAudio \"$ENABLE_AUDIO\" --es enableV2Audio \"$USE_RTC_APP_AUDIO\" --es enableRotationUsingAppOrientation \"$ENABLE_ROTATION_USING_APP_ORIENTATION\" --es enableBSIME \"$ENABLE_BS_IME\" --es handleMediaProjectionPermissionDuringRotation \"$HANDLE_MP_DURING_ROTATION\""
  adb -s $DEVICEID shell <<__EOF &
$start_network_service
$start_rtc_service
__EOF

  # handle mp in start only if it's not already provisioned
  mp_already_provisioned=$(ruby $RTCAPP_RELEASE_HELPER mp_already_provisioned $DEVICEID)
  if [ "$ENABLE_AUDIO" == "true" ] && [ "$AUTOMATE_STREAMING" == "false" ]; then
    if [ "$mp_already_provisioned" == "false" ]; then
      # Automation is faster than RTC app on some devices,
      # so the automation fails and Streaming doesn't start
      # TODO: Add instrumentation when popup fails
      sleep 1
      if [ "$SYNC_WEBRTC" == "true" ]; then
        handle_media_projection_popup
      else
        handle_media_projection_popup &
      fi
    else
      # locale is set for App Live inside handle_media_projection_popup after mp popup is handled
      # if mp_already_provisioned is true, handle_media_projection_popup won't be called
      # hence setting locale for App Live here
      change_locale
    fi
  fi
}

change_locale() {
  if [[ "$LOCALE_APP_LIVE" != "false" ]]; then
    lang_handler="am startservice --user 0 -n com.android.browserstack/.services.NetworkService --es task 'change_language' --es l \"$LOCALE_APP_LIVE\""
    adb -s $DEVICEID shell $lang_handler
  fi
}

stop_webrtc_app() {
	adb -s $DEVICEID shell <<__EOF
am startservice --user 0 -n $(rtc_app_use_package)/.RTCService --es "action" "stop" --es genre "$GENRE" --es free-space-path "$FREE_SPACE_PATH"
rm $MOBILE_RTC_CONFIG $TEMP_STATUSBAR_FILE
exit
__EOF
}

remove_flag_files() {
  adb -s $DEVICEID shell "rm $BROWSER_MODE_FILES $FIREFOX_START_STATUS $CHROME_START_STATUS $UC_START_STATUS $MOBILE_PACKAGE_FILE"
}

do_adb_device_check() {
  start=$(($(date +%s%N)/1000000))
  exit_code=`adb -s $DEVICEID shell "ls; echo $?" | tail -n1`
  if [ "${exit_code:0:1}" != "0" ]
  then
    echo "[$DEVICEID] missing over adb"
    $COMMON/push_to_cls.rb $CONFIG/rtc_service_$DEVICEID "device_not_on_adb" "" device_id:$DEVICEID
  fi
  end=$(($(date +%s%N)/1000000))
  timetaken=$((end-start))
  app_live_session_id=$(cat "/tmp/app_live_logs_params_${DEVICEID}" | cut -d " " -f2)
  $COMMON/push_to_eds.rb  "app_live_web_events" '{"adb_device_check_time":"'"$timetaken"'","event_name":"AndroidAdbCheck","session_id":"'"$app_live_session_id"'"}' --is_app_accessibility "$IS_APP_ACCESSIBILITY"

}

push_privoxy_to_cls() {
  paction_file=`cat $PACTION/paction-$DEVICEID.conf`
  proxy_file=`cat $CONFIG/$DEVICEID.txt`
  $COMMON/push_to_cls.rb $CONFIG/rtc_service_$DEVICEID "proxy_file" "$proxy_file" device_id:$DEVICEID
  $COMMON/push_to_cls.rb $CONFIG/rtc_service_$DEVICEID "paction_file" "$paction_file" device_id:$DEVICEID
}

notify_pusher() {
  product=${2:-"app_live"}
  # This is used by AppLive only
  if [ "$LOG_EVENTS" == "false" ]; then
    return
  fi

  session_params=$(cat "$CONFIG/rtc_service_${DEVICEID}")
  pusher_params=$(echo "$session_params" | jq -r '. | "type=app_live_dashboard&app_live_session_id=" + .app_live_session_id + "&channel=" + .pusher_channel + "&token=" + .pusher_auth ' | tr -d "\r\n")
  if [ "$product" == "live" ]; then
    pusher_params=$(echo "$session_params" | jq -r '. | "type=live_dashboard&live_session_id=" + .live_session_id + "&channel=" + .pusher_channel + "&token=" + .pusher_auth ' | tr -d "\r\n")
  fi
  echo "pusher_params: ${pusher_params}" >> /var/log/browserstack/server.log
  pusher_params="${pusher_params}&event=logs&message=${1}"
  pusher_url=$(echo "$session_params" | jq -r ".pusher_url" | tr -d "\r\n")
  curl -d "$pusher_params" "${pusher_url}/sendMessage"
}

notify_event() {
  notify_pusher "$1" "$3"
  cls_log_message "$1" "$2"
}

enable_audio_injection() {
  genre=$(jq -r .genre $STATE_FILES_DIR/session_"$DEVICEID")
  product=""
  session_id=""
  if [ "$genre" == "live_testing" ]; then
    session_id=$(jq -r .live_session_id $STATE_FILES_DIR/session_"$DEVICEID")
    product="live"
  else
    session_id=$(jq -r .app_live_session_id $STATE_FILES_DIR/session_"$DEVICEID")
    product="app_live"
  fi

  notify_event "Audio_Injection_Setup_Started,${session_id}" "" "$product"
  $BUNDLE exec ruby "$AUDIO_INJECTOR_HELPER" "setup" "$DEVICEID" "$session_id" "$product"
  status=$?
  if [ $status == 0 ]; then
    notify_event "Audio_Injection_Setup_Success,${session_id}" "" "$product"
  else
    notify_event "Audio_Injection_Setup_Failed,${session_id}" "" "$product"
  fi
}

block_sim_pages() {
  genre=$(jq -r .genre $STATE_FILES_DIR/session_"$DEVICEID")
  product=""
  session_id=""
  if [ "$genre" == "live_testing" ]; then
    session_id=$(jq -r .live_session_id $STATE_FILES_DIR/session_"$DEVICEID")
    product="live"
  else
    session_id=$(jq -r .app_live_session_id $STATE_FILES_DIR/session_"$DEVICEID")
    product="app_live"
  fi

  $BUNDLE exec ruby "$DEVICE_SIM_HELPER" "block_sim_pages" "$DEVICEID" "$session_id" "$product"
}

enable_sim() {
  genre=$(jq -r .genre $STATE_FILES_DIR/session_"$DEVICEID")
  product=""
  session_id=""
  if [ "$genre" == "live_testing" ]; then
    session_id=$(jq -r .live_session_id $STATE_FILES_DIR/session_"$DEVICEID")
    product="live"
  else
    session_id=$(jq -r .app_live_session_id $STATE_FILES_DIR/session_"$DEVICEID")
    product="app_live"
  fi

  notify_event "Android_SIM_Setup_Started,${session_id}" "" "$product"
  $BUNDLE exec ruby "$DEVICE_SIM_HELPER" "setup" "$DEVICEID" "$session_id" "$product"
  status=$?
  if [ $status == 0 ]; then
    notify_event "Android_SIM_Setup_Success,${session_id}" "" "$product"
  else
    notify_event "Android_SIM_Setup_Failed,${session_id}" "" "$product"
  fi
}

stop_logcat_capture() {
	ps aux | grep -v grep | grep "$DEVICEID logcat" | awk '{print $2}' | sudo xargs kill -9
	run_ruby_code $DEVICE_LOGGER_SCRIPT "$DEVICEID" "stop"
}

reset_logger(){
	adb -s $DEVICEID shell <<__EOF
	am startservice --user 0 -n com.android.browserstack/.services.NetworkService --es task "rtc_cls_init" --es ip "$IP"
	am startservice --user 0 -n $(rtc_app_use_package)/.RTCService --es "action" "reload"
	exit
__EOF
}

stop_webrtc_streaming() {
  ps aux | grep live_actions | grep start | grep $DEVICEID | awk '{print $2}' | sudo xargs kill -9
  rm -rf $STATE_FILES_DIR/session_$DEVICEID $CONFIG/rtc_service_$DEVICEID
  stop_webrtc_app
}
