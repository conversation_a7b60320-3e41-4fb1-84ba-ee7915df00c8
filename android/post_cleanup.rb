require_relative "constants"
require_relative 'models/android_device'
require_relative 'helpers/process_helper'
require_relative 'helpers/cleanup_stats'
require_relative 'helpers/battery_instrumentor'
require_relative 'lib/device_driver'
require_relative 'lib/browserstack_watcher_helper'
require_relative '../common/push_to_zombie'
require_relative '../common/hooter'
require_relative 'server'
require_relative 'helpers/battery_helper'
require_relative 'helpers/device_sim_helper'
require '/usr/local/.browserstack/mobile-common/utils/time_recorder'
require 'android_toolkit'
require 'logger'
require 'fileutils'
require 'time'
require 'browserstack_utils'

SERVER_PORT = "45671".freeze

class PostCleanup
  include TimeRecorder

  methods_to_wrap = %i[
    remove_session_related_files
    adb_session_cleanup
    remove_custom_deploy_file
    remove_expresso_session_files
    remove_fluttertest_session_files
    tell_rails_that_the_cleanup_is_done
    log_port_scanning_detection
    store_details_and_disable_sim
  ]

  # around_method takes a list of method names, and a block to be executed which wraps around the original method,
  # see helpers/method_interceptor.rb
  around_method(*methods_to_wrap) do |original_method, method_name|
    record_time(method_name, device_id, "android") { original_method.call }
  end

  attr_reader :device_id, :session_id

  def initialize(
    device_id,
    session_id,
    last_session_id,
    cleanup_type,
    last_session_type,
    reboot_end_time,
    reboot_start_time,
    cleanup_start_time,
    user_id,
    logger
  )
    @device_id = device_id
    @session_id = session_id
    @last_session_id = last_session_id
    @cleanup_type = cleanup_type
    @last_session_type = last_session_type
    @cleanup_start_time = cleanup_start_time.to_i
    @user_id = user_id
    @logger = logger
    @session_type = begin
      File.read("/tmp/sessionis_#{@device_id}").strip
    rescue StandardError
      ""
    end
    AndroidToolkit::Log.logger = @logger
    @reboot_time = reboot_end_time.to_i - reboot_start_time.to_i
  end

  def adb
    @adb ||= AndroidToolkit::ADB.new(udid: @device_id, path: BrowserStack::ADB)
  end

  def remove_session_related_files
    log :info, "Removing session related files"
    system(
      "sh #{BS_DIR}/mobile-common/mobile_session_info/lib/common-session.sh remove_mobile_session_dir #{@device_id}"
    )
    SESSION_RELATED_FILES.each do |file|
      file = file.gsub('device_id', @device_id)
      file = file.gsub('session_id', @session_id)
      if Dir.exist?(file)
        delete_files_in_directory(file)
        Dir.delete(file)
      end
      File.delete(file) if File.exist?(file)
    end
    files = ""
    ROOT_ACCESS_SESSION_RELATED_FILES.each do |file|
      file = file.gsub('device_id', @device_id)
      file = file.gsub('session_id', @session_id)
      files += "#{file} "
    end
    system("sudo rm #{files}") if files
  end

  def remove_custom_deploy_file
    custm_deploy_file = "/tmp/custom_deploy_#{@device_id}.sh"
    if File.exist?(custm_deploy_file)
      system("bash #{custm_deploy_file}")
      File.delete(custm_deploy_file)
    end
  end

  def remove_expresso_session_files
    if @session_type == "expresso" && Dir.exist?("/tmp")
      expresso_session_files = Dir["/tmp/espresso*_#{@device_id}"]
      expresso_session_files.each do |file|
        File.delete(file)
      end
    end
  end

  def remove_fluttertest_session_files
    if @session_type == "fluttertest" && Dir.exist?("/tmp")
      Dir["/tmp/fluttertest*_#{@device_id}"].each do |file|
        File.delete(file)
      end
    end
  end

  def tell_rails_that_the_cleanup_is_done
    device_driver = DeviceDriver.new(StaticConf::StaticConfHelper.setup, @device_id)
    device_driver.notify_cleanup_done
  end

  def log_cleanup_stats(total_cleanup_time)
    log :info, "Sending cleanup stats to zombie"
    stats_helper = CleanupStats.new(@device_id, @cleanup_type, total_cleanup_time, @session_id)
    stats_helper.log
    stats_helper.log_last_cleanup_stats
  end

  def log_port_scanning_detection
    log :info, "Sending device process tracking to zombie"
    process_helper = ProcessHelper.new(@device_id, @session_id)
    process_helper.log_port_scanning_detection
  end

  def store_details_and_disable_sim
    log :info, "Checking if SIM present on device and disabling it for public sim devices"
    if DeviceSIMHelper.sim?(@device_id)
      DeviceSIMHelper.sim_info(@device_id, force_update: true)
      if DeviceSIMHelper.public_sim?(@device_id)
        DeviceSIMHelper.disable_sim(@device_id, logger: @logger, from_post_cleanup: true)
      end
    end
  end

  def adb_session_cleanup
    adb.shell("echo '' > #{TEMP_CLEANUP_FILE}; rm -f #{SESSION_DIR}/*; rm -f #{TEMP_OPENED_BROWSERS_FILE}")
  end

  def push_cleanup_stats_to_zombie
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i, "Push cleanup stats to zombie started")
    total_cleanup_time = Time.now.to_i - @cleanup_start_time - @reboot_time
    zombie_key_value(
      kind: "android_#{@cleanup_type}",
      error: @last_session_type,
      data: total_cleanup_time,
      device: @device_id,
      session: @last_session_id,
      user: @user_id,
      user_os: @reboot_time
    )
    log_cleanup_stats(total_cleanup_time)
    BatteryInstrumentor.new(@device_id, @logger, @last_session_id).push('end_of_cleanup')
  end

  def launch_chrome
    adb.shell("am start -n com.android.chrome/com.google.android.apps.chrome.Main")
    adb.shell("input keyevent 3") #goto home screen
  end

  def enable_battery_life_optimizer
    battery_helper = BrowserStack::BatteryHelper.new(@device_id, adb.os_version.to_s, @logger)
    battery_helper.battery_life_optimizer
  end

  def instrument_battery_health
    # Deleting instrument_battery_udid in 1440 mins [1d] to push zombie once/day
    if file_older_than_minutes?(File.join(STATE_FILES_DIR, "instrument_battery_#{@device_id}"), 1440)
      FileUtils.rm_f("#{STATE_FILES_DIR}/instrument_battery_#{@device_id}")
    end

    battery_helper = BrowserStack::BatteryHelper.new(@device_id, adb.os_version.to_s, @logger)
    battery_data = battery_helper.instrument_dumpsys_battery
    return if battery_data.empty?

    zombie_push(
      "android",
      "android_battery_health",
      @last_session_type,
      adb.model,
      battery_data,
      @device_id,
      @last_session_id,
      '',
      adb.os_version.to_s
    )
  rescue StandardError => e
    log :info, "Unable to instrument battery health: #{e.message}"
  end

  def restart_watcher
    watcher_helper = WatcherHelper.new(@device_id, @session_id, "app_live", @logger)
    if watcher_helper.cont_scanning_state_file_exists?
      watcher_helper.delete_continuous_scanning_state_file # Delete state file to allow restart of watcher
      watcher_helper.restart
    end
  end

  def run
    BrowserStackUtils::LogCleanupFlow.log(@device_id, Time.now.to_i,
                                          "Started #{@device_id} #{@session_id} #{@session_type}")
    remove_session_related_files
    adb_session_cleanup
    remove_custom_deploy_file
    remove_expresso_session_files
    remove_fluttertest_session_files
    tell_rails_that_the_cleanup_is_done
    log_port_scanning_detection
    enable_battery_life_optimizer
    store_details_and_disable_sim
    instrument_battery_health
    launch_chrome #This is required in v108 More Details: AAP-7735
    restart_watcher
  end

  private

  def log(level, msg)
    @logger.send(level.to_sym, "#{self.class} #{msg}")
  end

  def delete_files_in_directory(path)
    # Deleting all the files in the directory
    Dir["#{path}/*"].each do |file_path|
      delete_files_in_directory(file_path) if Dir.exist?(file_path)
      File.delete(file_path)
    end
  end

  def file_older_than_minutes?(file_path, minutes)
    !File.exist?(file_path) || (File.mtime(file_path) < (Time.now - (60 * minutes)))
  end
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip.downcase.to_sym
  device_id = ARGV[1].to_s.strip
  session_id = ARGV[2].to_s.strip
  last_session_id = ARGV[3].to_s.strip
  cleanup_type = ARGV[4].to_s.strip
  last_session_type = ARGV[5].to_s.strip
  cleanup_start_time = ARGV[6].to_s.strip
  reboot_end_time = ARGV[7].to_s.strip
  reboot_start_time = ARGV[8].to_s.strip
  user_id = ARGV[9].to_s.strip
  helper = PostCleanup.new(
    device_id,
    session_id,
    last_session_id,
    cleanup_type,
    last_session_type,
    reboot_end_time,
    reboot_start_time,
    cleanup_start_time,
    user_id,
    Logger.new($stdout)
  )
  helper.send(command)
end
