{ pkgs, config }:

let
  pythonEnv = pkgs.python27.withPackages (ps: [ ps.pyudev ]);
  mobileServerLogs = "${config.logsPath}/service_realMobile.log";
  mobileCleanupLogs = "${config.logsPath}/service_cleanup.log";
  mobileCleanupPlatformServerLogs = "${config.logsPath}/service_cleanup_platform_server.log";
  mobileNetworkLogs = "${config.logsPath}/service_network_logs.log";
  mobileVideoLogs = "${config.logsPath}/service_video_logs.log";
  mobileOtherLogs = "${config.logsPath}/service_other_logs.log";
  mobileADBProxyLogs = "${config.logsPath}/adb_proxy.log";
  mobileADBWhitelistProxyLogs = "${config.logsPath}/adb_whitelist_proxy.log";
  chromeHarCapturerPort = "32348";
  mobileADBWhitelistProxyYaml = "${config.androidPlatform}/config/adb_commands.yml";
  mobileServerPumaConfig = "${config.androidPlatform}/android/deploy_conf/realMobile_server_config.rb";
  platformServerPumaConfig = "${config.androidPlatform}/android/deploy_conf/platform_server_config.rb";
  cleanupServerPumaConfig = "${config.androidPlatform}/android/deploy_conf/cleanup_server_config.rb";
  cleanupPlatformServerPumaConfig = "${config.androidPlatform}/android/deploy_conf/cleanup_platform_server_config.rb";
  networkLogsUploader = "${config.androidPlatform}/android/scripts/file_uploaders/network_logs_uploader_process.rb";
  videoLogsUploader = "${config.androidPlatform}/android/scripts/file_uploaders/video_logs_uploader_process.rb";
  otherLogsUploader = "${config.androidPlatform}/android/scripts/file_uploaders/other_logs_uploader_process.rb";
  cspServer = pkgs.callPackage ../csp {};
  chromeHarCapturerBinary = pkgs.callPackage ../chrome_har_capturer {};
in [
  {
    package = pkgs.writeTextFile {
    name = "usb_events_listener";
    executable = true;
    text = ''
      #!${pkgs.bash}/bin/bash
      MSG="USBEventsListener has stopped. Attempting to start again"
      sudo su - ${config.user} -c "${pythonEnv}/bin/python /usr/local/.browserstack/mobile/android/scripts/usb_events_listener.py"
    '';
  };
  destination = "/service/usb_events_listener/run";
  sudo = true;
  }
  {
    package = pkgs.writeTextFile {
      name = "mobile-server";
      executable = true;
      text = ''
        #!${pkgs.bash}/bin/bash
        file=${mobileServerLogs}
        exec 1>>$file
        exec 2>&1
        touch $file
        sudo su - ${config.user} -c "cd ${config.androidPlatform} && bundle exec puma --config ${mobileServerPumaConfig} --debug"
      '';
    };
    destination = "/service/mobile-server/run";
    sudo = true;
  }
  {
    package = pkgs.writeTextFile {
      name = "cleanup-server";
      executable = true;
      text = ''
        #!${pkgs.bash}/bin/bash
        file=${mobileCleanupLogs}
        exec 1<>$file
        exec 2>&1
        touch $file
        sudo su - ${config.user} -c "cd ${config.androidPlatform} && bundle exec puma --config ${cleanupServerPumaConfig} --debug"
      '';
    };
    destination = "/service/cleanup/run";
    sudo = true;
  }
  {
    package = pkgs.writeTextFile {
      name = "network-logs-uploader";
      executable = true;
      text = ''
        #!${pkgs.bash}/bin/bash
        file=${mobileNetworkLogs}
        exec 1>>$file
        exec 2>&1
        touch $file
        sudo su - ${config.user} -c "cd ${config.androidPlatform} && bundle exec ruby ${networkLogsUploader}"
      '';
    };
    destination = "/service/async_network_logs_processer/run";
    sudo = true;
  }
  {
    package = pkgs.writeTextFile {
      name = "video-logs-uploader";
      executable = true;
      text = ''
        #!${pkgs.bash}/bin/bash
        file=${mobileVideoLogs}
        exec 1>>$file
        exec 2>&1
        touch $file
        sudo su - ${config.user} -c "cd ${config.androidPlatform} && bundle exec ruby ${videoLogsUploader}"
      '';
    };
    destination = "/service/async_video_logs_processer/run";
    sudo = true;
  }
  {
    package = pkgs.writeTextFile {
      name = "other-logs-uploader";
      executable = true;
      text = ''
        #!${pkgs.bash}/bin/bash
        file=${mobileOtherLogs}
        exec 1>>$file
        exec 2>&1
        touch $file
        sudo su - ${config.user} -c "cd ${config.androidPlatform} && bundle exec ruby ${otherLogsUploader}"
      '';
    };
    destination = "/service/async_other_logs_processer/run";
    sudo = true;
  }
  {
    package = pkgs.writeTextFile {
      name = "adb_proxy";
      executable = true;
      text = ''
        #!${pkgs.bash}/bin/bash
        file=${mobileADBProxyLogs}
        touch $file
        sudo ruby ${config.androidPlatform}/android/helpers/adb_proxy.rb localhost:5037 5038 127.0.0.1 >> $file 2>&1
      '';
    };
    destination = "/service/adb_proxy/run";
    sudo = true;
  }
  {
    package = pkgs.writeTextFile {
      name = "adb_whitelist_proxy";
      executable = true;
      text = ''
        #!${pkgs.bash}/bin/bash
        file=${mobileADBWhitelistProxyLogs}
        touch $file
        sudo ${pkgs.ruby}/bin/ruby ${config.androidPlatform}/android/helpers/adb_whitelist_proxy.rb localhost:5037 5041 127.0.0.1 ${mobileADBWhitelistProxyYaml} >> $file 2>&1
      '';
    };
    destination = "/service/adb_whitelist_proxy_v2/run";
    sudo = true;
  }
  {
    package = pkgs.writeTextFile {
      name = "csp-server";
      executable = true;
      text = ''
        #!${pkgs.bash}/bin/bash
        file=/var/log/browserstack/service_csp.log
        exec 1>>$file
        exec 2>&1
        ENV=`cat /usr/local/.browserstack/env`
        if [ -z $ENV ] || [ "$ENV" != "prod" ]
        then
          ENV="staging"
        else
          ENV="production"
        fi
        CSPT_INFRA=BS CSPT_ENV=$ENV ${cspServer}/bs-perf-tool-linux server -p 45700
      '';
    };
    destination = "/service/csp-server/run";
    sudo = true;
  }
  {
    package = pkgs.writeTextFile {
      name = "chrome-har-capturer-server";
      executable = true;
      text = ''
        #!${pkgs.bash}/bin/bash
        logfile="${config.logsPath}/chrome_har_capturer_${chromeHarCapturerPort}.log";
        exec 1>>$logfile
        exec 2>&1
        echo "Starting chrome-har-capturer server on port ${chromeHarCapturerPort}"
        sudo su - ${config.user} -c "${chromeHarCapturerBinary} ${chromeHarCapturerPort}"
      '';
    };
    destination = "/service/chrome_har_capturer_server_v3/run";
    sudo = true;
  }
]
