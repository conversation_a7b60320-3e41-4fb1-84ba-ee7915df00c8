{ stdenv
, fetchzip
, nodejs-12_x
, writeText
}:

let
  GITHUB_TOKEN = builtins.getEnv "GITHUB_TOKEN";
in
stdenv.mkDerivation rec {
  pname = "chrome-har-capturer";
  version = "0.15.0";
  revision = "2c271bcd201f0e891b730171ffd29805c7d0f020";
  src = fetchzip {
    url = "https://github.com/browserstack/${pname}/archive/${revision}.tar.gz";
    sha256 = "19fvj0w91hrb2nmlk4z012ni026nhdw56zfb8xwp4llyvm7fwxdh";
    curlOpts = [ ''-H @${writeText "headers.txt" "Authorization: token ${GITHUB_TOKEN}"}'' ];
  };
  buildInputs = [
    nodejs-12_x
  ];
  phases = [ "installPhase" ];
  installPhase = ''
    set -e
    HOME=$TMPDIR
    shopt -s dotglob
    cp -R $src/* .
    npm install
    npx eslint . --fix
    npx pkg server.js --targets node12-linux-x64 --output $out
  '';
}
