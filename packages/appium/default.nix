{ pkgs }:
let appium = pkgs.callPackage ./appium.nix {};
in [
  {
    package = appium {
      revision = "8a55e578ce5e39dd7bdd206237a3b9580abcc5d4";
      sha256 = "0b07s6aidbisjgjmj83gbfwpiii8w1j70qnmyd598wclwv82iyrv";
      version = "1.10.1";
    };
    destination = "/usr/local/.browserstack/appium_1.10.1_bstack";
  }
  {
    package = appium {
      revision = "51529f2404a8b1a180f14aac9cab0d5579ebfbe0";
      sha256 = "1dqvzgyjfgsrjz8kjc8wiz4nf7lynhxp2d7k6wgnkvyv36pns1gp";
      version = "1.11.1";
    };
    destination = "/usr/local/.browserstack/appium_1.11.1_bstack";
  }
  {
    package = appium {
      revision = "e5e10caca8c603c9aadb776ac365cbc269f71f3d";
      sha256 = "1xrilqca9g1cn9sj8fgkv0s2iw4rbr4rnha4kcwwd86mxwz8r1cx";
      version = "1.12.1";
    };
    destination = "/usr/local/.browserstack/appium_1.12.1_bstack";
  }
  {
    package = appium {
      revision = "ed7a457ae34ab6b93134cfbad0e9fe3dfae87d98";
      sha256 = "1bk39nn70639iyw64np7salpc3y9qjgcrykhignjwp10iyjcxgv0";
      version = "1.13.0";
    };
    destination = "/usr/local/.browserstack/appium_1.13.0_bstack";
  }
  {
    package = appium {
      revision = "19320fab59ed62f858703025a959a505ea9f1ba4";
      sha256 = "00d8pk4lgjr8zs00fgjl4qvrhd9fnl4amb0zxsx2qsmyiv3mj6mj";
      version = "1.14.0";
    };
    destination = "/usr/local/.browserstack/appium_1.14.0_bstack";
  }
  {
    package = appium {
      revision = "5abc73d005ce0c11c54bcadbf601af1dbe794883";
      sha256 = "1qw4nvl9a0zx3f0s09kp60ihg1vp058q2s11b3ns8zqz9kbdy2dw";
      version = "1.15.0";
    };
    destination = "/usr/local/.browserstack/appium_1.15.0_bstack";
  }
  {
    package = appium {
      revision = "dd1040fcc591399ee850003cd4ac2549c8879483";
      sha256 = "1avmkdfv9nj6a5rqq5x8p1qjrfagq2k7fi3cgyknypid40w5wsc7";
      version = "1.16.0";
    };
    destination = "/usr/local/.browserstack/appium_1.16.0_bstack";
  }
  {
    package = appium {
      revision = "ccced93f2a1ea99066b80ff11739b69de3a486dc";
      sha256 = "1zh39dcjrix7xazvrs2py8i7pc6fk1rwgl3lnnm6pp83fj0839as";
      version = "1.17.0";
    };
    destination = "/usr/local/.browserstack/appium_1.17.0_bstack";
  }
  {
    package = appium {
      revision = "9300d344b19f7c67522e69d47f53567297a0e923";
      sha256 = "0w8zgpm6p4kdmdz2cicfmrsj0q9fdgp91p26jyjampnwbmqk5n2r";
      version = "1.18.0";
    };
    destination = "/usr/local/.browserstack/appium_1.18.0_bstack";
  }
  {
    package = appium {
      revision = "483e46f401505ff37844c45d2aac29d994a43f90";
      sha256 = "0xjk2dryk4ma32pm6hvqchmrqg4jbaw9c83a08fqw3x5a1kpz03b";
      version = "1.19.1";
    };
    destination = "/usr/local/.browserstack/appium_1.19.1_bstack";
  }
  {
    package = appium {
      revision = "578f8f57a9506b8d0b65cabd1723c35cb0416e93";
      sha256 = "1fv5kn7hlha6bdjws068yqd0i5a9k7gbfmmm1n9ckxw7psm6h6p2";
      version = "1.20.2";
    };
    destination = "/usr/local/.browserstack/appium_1.20.2_bstack";
  }
  {
    package = appium {
      revision = "3d03b89b142c42a286162da0ffc6ab52b0b5971b";
      sha256 = "0k1pch3gmnrzni7ghlcxhvxkmbwb8kvr29kiqwibcwp92iy1zymx";
      version = "1.21.0";
    };
    destination = "/usr/local/.browserstack/appium_1.21.0_bstack";
  }
  {
    package = appium {
      revision = "3a822df15eb4a85bfddad3cf16e01dcddcce34fb";
      sha256 = "1kav1liy8hmwij3s56c10rbcjc3w7y2qn0hj3ssr72h4kxs12g3s";
      version = "1.22.0";
    };
    destination = "/usr/local/.browserstack/appium_1.22.0_bstack";
  }
  {
    package = appium {
      revision = "52db7cf6687ca9720d6fec4a36fd19cf65f76963";
      sha256 = "1522db6s8d1kcnf00xy0islxpmddgrhfyr7kxk9f3n5x7szl5wr9";
      version = "1.6.5";
    };
    destination = "/usr/local/.browserstack/appium_1.6.5_bstack";
  }
  {
    package = appium {
      revision = "5a789699d2511a2f38bc663f56641587bd55eacb";
      sha256 = "0wj0mw3b475gl2qxw8kgszif10z1nsnpclgfqlhj04aga81y8c56";
      version = "1.7.1";
    };
    destination = "/usr/local/.browserstack/appium_1.7.1_bstack";
  }
  {
    package = appium {
      revision = "8391e68c03a09f3b29bc1a6b8f1d44eb226a82e1";
      sha256 = "0ac9d6vg5w8bziy5dr9bv0p7p17p8cgli1xh38n670sn2knymsyi";
      version = "1.7.2";
    };
    destination = "/usr/local/.browserstack/appium_1.7.2_bstack";
  }
  {
    package = appium {
      revision = "1fc0211baa72e3e917444a52a232ca449461628c";
      sha256 = "091hr7ik9dmfhwh5pl3hvfvr27rcznrrc2c68wsl75ksfdf0xc71";
      version = "1.8.0";
    };
    destination = "/usr/local/.browserstack/appium_1.8.0_bstack";
  }
  {
    package = appium {
      revision = "2f4021acd5dde63be4bbe45cc2a59d9fbb8fb994";
      sha256 = "1hx00ridwrqimv7akid3plm62zab4rba2l73afgm68ghisrmzjs0";
      version = "1.9.1";
    };
    destination = "/usr/local/.browserstack/appium_1.9.1_bstack";
  }
  {
    package = appium {
      revision = "9c32115369264db2bd665f39e4b6293392ae5a69";
      sha256 = "1rhmvs3q1zzqxzgzj1ldhqjzxm5av08mlr7yxqrjwdqwc14vfs5n";
      version = "2.0.0";
    };
    destination = "/usr/local/.browserstack/appium_2.0.0_bstack";
  }
  {
    package = appium {
      revision = "08be14a2e936aebbd7b94c32d262ea0322718a9e";
      sha256 = "0vnsfhhpzvx0wzgllr6mc8sp7132chhn33z16vw7lqr958sf42h6";
      version = "2.0.1";
    };
    destination = "/usr/local/.browserstack/appium_2.0.1_bstack";
  }
  {
    package = appium {
      revision = "73250ea803cf5e50d4ca2bd0c5977e67b8422b2d";
      sha256 = "0fzydk4a1a5r7kgl8h6a2q7vmp3g9adqjjinh21c0mkzs2pqmc2v";
      version = "2.4.1";
    };
    destination = "/usr/local/.browserstack/appium_2.4.1_bstack";
  }
  {
    package = appium {
      revision = "cae37965be64f7eab031e71b3feb5aad4b154a8c";
      sha256 = "1yybxxnb0g2xfs5yhzbxkxpmrx70prrcyhbqi474d2wylsnkhvzg";
      version = "2.6.0";
    };
    destination = "/usr/local/.browserstack/appium_2.6.0_bstack";
  }
  {
    package = appium {
      revision = "8413f7da20623cbee85781f0309c4fbb981b0ad4";
      sha256 = "0gs89gwydbv6dfhdsw8d3ngpynm8zicsrfjsf8304f3zwkhlryz6";
      version = "2.12.1";
    };
    destination = "/usr/local/.browserstack/appium_2.12.1_bstack";
  }
  {
    package = appium {
      revision = "bc10dc7028421ea0aca8566ee5e49457b3764bb3";
      sha256 = "0skk1yibh962ds4zdqzcz8ksbv1qqmwncppfwxfc4p6qgsr6na52";
      version = "2.15.0";
    };
    destination = "/usr/local/.browserstack/appium_2.15.0_bstack";
  }
]

