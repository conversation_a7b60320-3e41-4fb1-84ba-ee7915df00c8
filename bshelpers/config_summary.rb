#!/usr/bin/env ruby

# TODO: it would be cool to pad everything to have tabular output
require 'json'
require_relative '../android/constants'
config_file = '/usr/local/.browserstack/config/config.json'

BLUE = "\033[94m".freeze
YELLOW = "\033[93m".freeze
GREEN = "\033[92m".freeze
RED = "\033[91m".freeze
BOLD = "\033[1m".freeze
HIGHLIGHT = "\033[48;5;242m".freeze
SESSION = "⚠️ ".freeze

ENDC = "\033[0m".freeze

conf = JSON.parse File.read config_file

phone = ARGV[0]
fancy_model = ARGV.include? '-m'

$models = {
  "iPad11,1" => "iPad Mini 2019",
  "iPad11,2" => "iPad Mini 2019",
  "iPad11,3" => "iPad Air 2019",
  "iPad11,4" => "iPad Air 2019",
  "iPad3,4" => "iPad 4",
  "iPad3,5" => "iPad 4",
  "iPad3,6" => "iPad 4",
  "iPad4,1" => "iPad Air",
  "iPad4,2" => "iPad Air",
  "iPad4,3" => "iPad Air",
  "iPad4,4" => "iPad Mini 2",
  "iPad4,5" => "iPad Mini 2",
  "iPad4,6" => "iPad Mini 2",
  "iPad4,7" => "iPad Mini 3",
  "iPad4,8" => "iPad Mini 3",
  "iPad5,2" => "iPad Mini 4",
  "iPad5,3" => "iPad Air 2",
  "iPad5,4" => "iPad Air 2",
  "iPad6,11" => "iPad 5th",
  "iPad6,3" => "iPad Pro 9.7 2016",
  "iPad7,11" => "iPad 7th",
  "iPad7,12" => "iPad 7th",
  "iPad7,2" => "iPad Pro 12.9",
  "iPad7,6" => "iPad 6th",
  "iPad8,10" => "iPad Pro 11 2020",
  "iPad8,12" => "iPad Pro 12.9 2020",
  "iPad8,3" => "iPad Pro 11 2018",
  "iPad8,7" => "iPad Pro 12.9 2018",
  "iPhone10,1" => "iPhone 8",
  "iPhone10,2" => "iPhone 8 Plus",
  "iPhone10,3" => "iPhone X",
  "iPhone10,4" => "iPhone 8",
  "iPhone10,5" => "iPhone 8 Plus",
  "iPhone10,6" => "iPhone X",
  "iPhone11,2" => "iPhone XS",
  "iPhone11,6" => "iPhone XS Max",
  "iPhone11,8" => "iPhone XR",
  "iPhone12,1" => "iPhone 11",
  "iPhone12,3" => "iPhone 11 Pro",
  "iPhone12,5" => "iPhone 11 Pro Max",
  "iPhone12,8" => "iPhone SE 2020",
  "iPhone5,1" => "iPhone 5",
  "iPhone5,2" => "iPhone 5",
  "iPhone6,1" => "iPhone 5S",
  "iPhone6,2" => "iPhone 5S",
  "iPhone7,1" => "iPhone 6 Plus",
  "iPhone7,2" => "iPhone 6",
  "iPhone8,1" => "iPhone 6S",
  "iPhone8,2" => "iPhone 6S Plus",
  "iPhone8,4" => "iPhone SE",
  "iPhone9,1" => "iPhone 7",
  "iPhone9,3" => "iPhone 7",
  "iPhone9,4" => "iPhone 7 Plus"
}

def name_from_model(config_for_phone)
  db_file = BrowserStack::DATABASE_PATH
  model_manufacturer = config_for_phone['device_name']
  if config_for_phone['os'] == 'android'
    `sqlite3 #{db_file} "select display_name from models where name = '#{model_manufacturer}';"`.split("\n").first.strip
  else
    $models[model_manufacturer]
  end
end

realmobile_dir = '/usr/local/.browserstack/realmobile/'

conf['devices'].each do |k, v|
  cleanup_reason = if File.directory?(realmobile_dir)
                     `sqlite3 /usr/local/.browserstack/cleanup_status.sqlite \
                      "select failure_reason from cleanups where deviceid='#{k}'"`.strip
                   else
                     begin
                       File.read("/tmp/cleanup_reason_#{k}").strip
                     rescue StandardError
                       ''
                     end
                   end
  cleanup_reason = "[#{cleanup_reason}]" if cleanup_reason != ''
  badge = if v['online']
            "#{GREEN}✓#{ENDC}"
          elsif v['active_session'].to_s == 'true'
            SESSION
          else
            "#{RED}✗#{ENDC}"
          end
  udid = k
  offline_reason = v['offline_reason']
  offline_reason += " #{cleanup_reason}" if offline_reason&.include? 'clean'

  model = fancy_model ? name_from_model(v) : v['device_name']
  version = v['device_version']
  highlighter = phone == udid ? HIGHLIGHT : ""
  puts "#{highlighter}#{udid} #{model} (#{version}) #{badge} #{offline_reason} #{ENDC}"
end
