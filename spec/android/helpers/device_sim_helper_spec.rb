require_relative '../../../android/helpers/device_sim_helper'
require_relative '../../spec_helper'

describe DeviceSIMHelper do
  let(:device) { "ABCDEFGH" }
  let(:state_files_dir) { "/usr/local/.browserstack/state_files" }
  let(:params) do
    {
      device: device,
      session_id: '123',
      product: 'app_live'
    }
  end
  let(:subject) { DeviceSIMHelper.new(params) }
  let(:adb) { double("mock_adb") }
  let(:device_obj) { double("mock_device_obj") }
  let(:eds_obj) { double("mock_eds") }
  let(:device_owner_manager) { double('device_owner_manager') }

  let(:package_path) do
    "package:/data/app/~~tbm4A9pEgYB1n6RP9oFFCw==/com.android.browserstack-D9WNQKJXh7MTOcMiLtuWRw==/base.apk"
  end
  let(:method_name) { "setSimPowerStateForSlot" }
  let(:rssi_good) do
    "mSignalStrength=SignalStrength:{mCdma=CellSignalStrengthCdma: cdmaDbm=2147483647 cdmaEcio=2147483647
     evdoDbm=2147483647 evdoEcio=2147483647 evdoSnr=2147483647 level=0,mGsm=CellSignalStrengthGsm: rssi=2147483647
     ber=2147483647 mTa=2147483647 mLevel=0,mWcdma=CellSignalStrengthWcdma: ss=2147483647 ber=2147483647
     rscp=2147483647 ecno=2147483647 level=0,mTdscdma=CellSignalStrengthTdscdma: rssi=2147483647 ber=2147483647
     rscp=2147483647 level=0,mLte=CellSignalStrengthLte: rssi=-51 rsrp=-78 rsrq=-9 rssnr=30 cqiTableIndex=2147483647
     cqi=2147483647 ta=2147483647 level=4 parametersUseForLevel=0,mNr=CellSignalStrengthNr:{ csiRsrp = 2147483647
     csiRsrq = 2147483647 csiCqiTableIndex = 2147483647 csiCqiReport = [] ssRsrp = 2147483647 ssRsrq = 2147483647
     ssSinr = 2147483647 level = 0 parametersUseForLevel = 0 },primary=CellSignalStrengthLte}"
  end
  let(:rssi_bad) do
    "mSignalStrength=SignalStrength:{mCdma=CellSignalStrengthCdma: cdmaDbm=2147483647 cdmaEcio=2147483647
     evdoDbm=2147483647 evdoEcio=2147483647 evdoSnr=2147483647 level=0,mGsm=CellSignalStrengthGsm: rssi=2147483647
     ber=2147483647 mTa=2147483647 mLevel=0,mWcdma=CellSignalStrengthWcdma: ss=2147483647 ber=2147483647
     rscp=2147483647 ecno=2147483647 level=0,mTdscdma=CellSignalStrengthTdscdma: rssi=2147483647 ber=2147483647
     rscp=2147483647 level=0,mLte=CellSignalStrengthLte: rssi=-115 rsrp=-78 rsrq=-9 rssnr=30 cqiTableIndex=2147483647
     cqi=2147483647 ta=2147483647 level=4 parametersUseForLevel=0,mNr=CellSignalStrengthNr:{ csiRsrp = 2147483647
     csiRsrq = 2147483647 csiCqiTableIndex = 2147483647 csiCqiReport = [] ssRsrp = 2147483647 ssRsrq = 2147483647
     ssSinr = 2147483647 level = 0 parametersUseForLevel = 0 },primary=CellSignalStrengthLte}"
  end
  let(:device_config) do
    {
      region: "us-east-1",
      sub_region: "us-east-1c"
    }
  end

  before(:each) do
    allow(AndroidToolkit::ADB).to receive(:new).and_return(adb)
    allow(DeviceOwnerManager).to receive(:new).with(params[:device], @mock_logger).and_return(device_owner_manager)
    allow(BrowserStack::AndroidDevice).to receive(:new).and_return(device_obj)
    allow(EDS).to receive(:new).and_return(eds_obj)
    allow(eds_obj).to receive(:push_logs).and_return(true)
    allow(device_obj).to receive(:id).and_return(device)
    allow(device_obj).to receive(:common_name).and_return("Samsung Galaxy s22")
    allow(device_obj).to receive(:os_version).and_return("12.0")
    allow(device_obj).to receive(:device_config).and_return(device_config)
    allow(BrowserStack.logger).to receive(:error)
    allow(BrowserStack.logger).to receive(:info)
    allow(OSUtils).to receive(:execute)
    allow(File).to receive(:write)
    allow(File).to receive(:read)
    allow(File).to receive(:exist?)
  end

  describe 'sim?' do
    context 'when the device contains a SIM' do
      sim_path = "/usr/local/.browserstack/mobile/config/custom_devices/android_sim_devices"
      before do
        allow(BrowserStack.logger).to receive(:info)
      end
      it 'returns true' do
        allow(DeviceSIMHelper).to receive(:public_sim?).with('device_id').and_return(true)
        expect(DeviceSIMHelper.sim?('device_id')).to eq(true)
      end
    end

    context 'when the device does not contain a SIM' do
      it 'returns false' do
        allow(DeviceSIMHelper).to receive(:public_sim?).with('device_id').and_return(false)
        allow(DeviceSIMHelper).to receive(:private_sim?).with('device_id').and_return(false)
        expect(DeviceSIMHelper.sim?('device_id')).to eq(false)
      end
    end

    context 'when an error occurs' do
      before do
        allow(OSUtils).to receive(:execute).and_raise(StandardError)
        allow(DeviceState).to receive(:new).and_raise(StandardError)
      end

      it 'returns false' do
        expect(DeviceSIMHelper.sim?('device_id')).to eq(false)
      end
    end
  end

  describe '.fetch_sim_prop' do
    let(:device_owner_manager) { double('device_owner_manager') }
    it 'should return sim info using service call when device owner not supported' do
      allow(device_obj).to receive(:model).and_return("ABCD")
      allow(device_obj).to receive(:model).and_return("ABCD")
      allow(device_obj).to receive(:supports_device_owner?).and_return(false)
      expect(DeviceSIMHelper).to receive(:iphonesubinfo_command).and_return('10')

      expect(DeviceSIMHelper.fetch_sim_prop(device, "msisdn", true)).to eq('10')
    end
    it 'should return sim info using device owner application files when device owner supported' do
      allow(device_obj).to receive(:model).and_return("ABCD")
      allow(device_obj).to receive(:supports_device_owner?).and_return(true)
      expect(adb).to receive(:am).and_return('')
      expect(device_owner_manager).to receive(:privileges_enabled?).and_return(true)
      expect(adb).to receive(:shell).with(
        "run-as com.browserstack.deviceowner cat /data/data/com.browserstack.deviceowner/files/msisdn.prop"
      ).and_return("10")
      expect(DeviceSIMHelper.fetch_sim_prop(device, "msisdn", true)).to eq('10')
    end
  end

  describe '.fetch_sim_signal_strength' do
    let(:device) { 'device_id' }
    let(:carrier) { 'CarrierName' }
    let(:sim_slot) { 1 }
    let(:adb_double) { instance_double(AndroidToolkit::ADB) }
    let(:android_device_double) { instance_double(BrowserStack::AndroidDevice, os_version: '11') }

    before do
      allow(AndroidToolkit::ADB).to receive(:new).with(udid: device, path: BrowserStack::ADB).and_return(adb_double)
      allow(BrowserStack::AndroidDevice).to receive(:new).with(device, "DeviceSimHelper",
                                                               BrowserStack.logger).and_return(android_device_double)
      allow(DeviceSIMHelper).to receive(:zombie_push)
      allow(DeviceSIMHelper).to receive(:dedicated_device?).with(device).and_return(false)
    end

    context 'when signal strength is successfully fetched' do
      it 'returns the correct bars and dBm for the first SIM slot' do
        allow(adb_double).to receive(:shell).with(/dumpsys telephony.registry/).and_return("-85\n-90")
        allow(DeviceSIMHelper).to receive(:get_bars_from_dbm_value).with(-85).and_return(3)

        result = DeviceSIMHelper.fetch_sim_signal_strength(device, carrier, sim_slot)

        expect(result).to eq({ "Bars" => 3, "dBm" => -85 })
      end

      it 'returns the correct bars and dBm for the second SIM slot' do
        sim_slot = 2
        allow(adb_double).to receive(:shell).with(/dumpsys telephony.registry/).and_return("-85\n-90")
        allow(DeviceSIMHelper).to receive(:get_bars_from_dbm_value).with(-90).and_return(2)

        result = DeviceSIMHelper.fetch_sim_signal_strength(device, carrier, sim_slot)

        expect(result).to eq({ "Bars" => 2, "dBm" => -90 })
      end
    end

    context 'when signal strength cannot be fetched' do
      it 'logs an info message and returns "Unknown" and "N/A"' do
        allow(adb_double).to receive(:shell).with(/dumpsys telephony.registry/).and_return("")
        allow(BrowserStack.logger).to receive(:info)

        result = DeviceSIMHelper.fetch_sim_signal_strength(device, carrier, sim_slot)

        expect(result).to eq({ "Bars" => "Unknown", "dBm" => "N/A" })
        expect(BrowserStack.logger).to have_received(:info).with(/Unable to retrieve signal strength/)
      end
    end

    context 'when an error occurs during execution' do
      it 'logs an error message and returns "Unknown" and "N/A"' do
        allow(adb_double).to receive(:shell).and_raise(StandardError.new("ADB error"))
        allow(BrowserStack.logger).to receive(:error)

        result = DeviceSIMHelper.fetch_sim_signal_strength(device, carrier, sim_slot)

        expect(result).to eq({ "Bars" => "Unknown", "dBm" => "N/A" })
        expect(BrowserStack.logger).to have_received(:error).with(/Failed to fetch signal strength, error: ADB error/)
      end
    end

    context 'when Android version is below 10' do
      it 'uses the GSM signal strength command' do
        allow(android_device_double).to receive(:os_version).and_return('9')
        allow(adb_double).to receive(:shell).with(/dumpsys telephony.registry.*cut -d' ' -f14/).and_return("-85\n-90")
        allow(DeviceSIMHelper).to receive(:get_bars_from_dbm_value).with(-85).and_return(3)

        result = DeviceSIMHelper.fetch_sim_signal_strength(device, carrier, sim_slot)

        expect(result).to eq({ "Bars" => 3, "dBm" => -85 })
      end
    end

    context 'when the signal strength data is pushed to BQ' do
      it 'sends the correct signal strength data' do
        allow(adb_double).to receive(:shell).with(/dumpsys telephony.registry/).and_return("-85\n-90")
        allow(DeviceSIMHelper).to receive(:get_bars_from_dbm_value).with(-85).and_return(3)

        expect(DeviceSIMHelper).to receive(:zombie_push).with(
          "android",
          "sim-signal-strength",
          "",
          "",
          {
            "carrier" => carrier,
            "signal_strength" => "3 bars",
            "dbm" => -85,
            "sim_slot" => sim_slot,
            "dedicated_device" => false
          }.to_json,
          device
        )

        DeviceSIMHelper.fetch_sim_signal_strength(device, carrier, sim_slot)
      end
    end
  end

  describe '.check_update_signal_strength' do
    let(:device) { 'test_device' }
    let(:carrier) { 'Jio' }
    let(:sim_slot) { 1 }
    let(:file_path) { "#{BrowserStack::STATE_FILES_DIR}/android_sim_#{device}_#{sim_slot - 1}.yml" }
    let(:existing_data) do
      {
        'carrier' => carrier,
        'sim_slot' => sim_slot,
        'last_updated' => Time.now.to_i - 200,
        'signal_strength' => { "Bars" => 3, "dBm" => -85 }
      }
    end

    before do
      allow(File).to receive(:exist?).with(file_path).and_return(true)
      allow(YAML).to receive(:load_file).with(file_path).and_return(existing_data)
    end

    context 'when the file exists and carrier and sim_slot match' do
      it 'returns updated signal strength when last updated time exceeds 1 hour' do
        existing_data['last_updated'] = Time.now.to_i - 3700
        allow(DeviceSIMHelper).to receive(:fetch_sim_signal_strength)
          .with(device, carrier, sim_slot)
          .and_return({ "Bars" => 3, "dBm" => -85 })

        result = DeviceSIMHelper.check_update_signal_strength(device, carrier, sim_slot)
        expect(result[:signal_strength]).to eq({ "Bars" => 3, "dBm" => -85 })
        expect(result[:last_updated_signal_strength]).to eq(Time.now.round)
      end

      it 'returns existing signal strength when last updated time is within 1 hour' do
        existing_data['last_updated'] = Time.now.to_i - 100

        result = DeviceSIMHelper.check_update_signal_strength(device, carrier, sim_slot)
        expect(result[:signal_strength]).to eq({ "Bars" => 3, "dBm" => -85 })
        expect(result[:last_updated_signal_strength]).to eq(existing_data['last_updated'])
      end

      it 'fetches signal strength if last_updated is nil' do
        existing_data['last_updated'] = nil
        allow(DeviceSIMHelper).to receive(:fetch_sim_signal_strength)
          .with(device, carrier, sim_slot)
          .and_return({ "Bars" => 2, "dBm" => -90 })

        result = DeviceSIMHelper.check_update_signal_strength(device, carrier, sim_slot)
        expect(result[:signal_strength]).to eq({ "Bars" => 2, "dBm" => -90 })
        expect(result[:last_updated_signal_strength]).to eq(Time.now.round)
      end
    end

    context 'when the file does not exist' do
      before do
        allow(File).to receive(:exist?).with(file_path).and_return(false)
      end

      it 'returns nil' do
        result = DeviceSIMHelper.check_update_signal_strength(device, carrier, sim_slot)
        expect(result).to be_nil
      end
    end

    context 'when the file does not exist' do
      before do
        allow(File).to receive(:exist?).with(file_path).and_return(false)
      end

      it 'returns nil' do
        result = DeviceSIMHelper.check_update_signal_strength(device, carrier, sim_slot)
        expect(result).to be_nil
      end
    end

    context 'when the carrier or sim_slot do not match' do
      before do
        existing_data['carrier'] = 'Airtel'
      end

      it 'returns nil' do
        result = DeviceSIMHelper.check_update_signal_strength(device, carrier, sim_slot)
        expect(result).to be_nil
      end
    end

    context 'when signal strength fetch fails' do
      before do
        existing_data['last_updated'] = nil
        allow(DeviceSIMHelper).to receive(:fetch_sim_signal_strength).and_return(nil)
      end

      it 'logs the issue and returns nil' do
        result = DeviceSIMHelper.check_update_signal_strength(device, carrier, sim_slot)
        expect(result[:signal_strength]).to be_nil
      end
    end
  end

  describe "#setup" do
    before(:each) do
      allow(subject).to receive(:go_to_home_screen)
      allow(DeviceSIMHelper).to receive(:sim?).and_return(true)
    end

    it "should enable sim, detect good signal strength, clear calls and sms logs and return true" do
      allow(device_owner_manager).to receive(:privileges_enabled?).and_return(true)
      expect(adb).to receive(:shell).with(
        "pm path com.android.browserstack"
      ).and_return(package_path)
      expect(adb).to receive(:shell).with(
        "CLASSPATH=#{package_path.split(':')[1].chomp} app_process / com.android.browserstack.Main #{method_name} 0 1"
      ).and_return("")
      expect(device_owner_manager).to receive(:enable_sms)
      expect(subject).to receive(:telephony_cleanup).and_return(true)
      expect(subject).to receive(:validate).exactly(3).times.and_return(true)
      expect(adb).to receive(:shell).with(
        "touch /sdcard/public_sim_session"
      ).and_return("")
      expect(adb).to receive(:shell).with(
        "dumpsys telephony.registry | grep -i mSignalStrength"
      ).and_return(rssi_good)
      expect(subject).to_not receive(:zombie_push)
      expect(subject).to receive(:get_rule_for_sim_step).and_return(["programmatic", "", nil])
      expect(subject).to receive(:send_data_to_eds).exactly(2).times.and_return(true)
      expect(subject.setup).to eq(true)
    end

    it "should enable sim, detect bad signal strength and return error" do
      allow(device_owner_manager).to receive(:privileges_enabled?).and_return(true)
      expect(adb).to receive(:shell).with(
        "pm path com.android.browserstack"
      ).and_return(package_path)
      expect(adb).to receive(:shell).with(
        "CLASSPATH=#{package_path.split(':')[1].chomp} app_process / com.android.browserstack.Main #{method_name} 0 1"
      ).and_return("")
      expect(device_owner_manager).to receive(:enable_sms)
      allow(adb).to receive(:shell).with(
        "dumpsys telephony.registry | grep -i mSignalStrength"
      ).and_return(rssi_bad)
      expect(subject).to receive(:get_rule_for_sim_step).and_return(["programmatic", "", nil])
      expect(subject).to receive(:zombie_push)
      expect(subject).to receive(:send_data_to_eds).exactly(2).times.and_return(true)
      expect { subject.setup }.to raise_error("bad signal strength")
    end

    it "should perform setup steps but fail at validation and return false" do
      allow(device_owner_manager).to receive(:privileges_enabled?).and_return(true)
      expect(adb).to receive(:shell).with(
        "pm path com.android.browserstack"
      ).and_return(package_path)
      expect(adb).to receive(:shell).with(
        "CLASSPATH=#{package_path.split(':')[1].chomp} app_process / com.android.browserstack.Main #{method_name} 0 1"
      ).and_return("")
      expect(device_owner_manager).to receive(:enable_sms)
      expect(adb).to receive(:shell).with(
        "dumpsys telephony.registry | grep -i mSignalStrength"
      ).and_return(rssi_good)
      expect(subject).to receive(:telephony_cleanup).and_return(true)
      expect(subject).to receive(:validate).and_return(false)
      expect(subject).to receive(:zombie_push)
      expect(subject).to receive(:get_rule_for_sim_step).and_return(["programmatic", "", nil])
      expect(subject).to receive(:send_data_to_eds).exactly(2).times.and_return(true)
      expect { subject.setup }.to raise_error("setup validation failure")
    end
  end

  describe 'sim_info' do
    context 'if sim is true' do
      before(:each) do
        expect(DeviceSIMHelper).to receive(:sim?).and_return(true)
        allow(AndroidToolkit::ADB).to receive(:new).and_return(adb)
        allow(File).to receive(:write).and_return(true)
      end

      it 'should return sim info from device when force -> false and state file -> not present' do
        allow(DeviceSIMHelper).to receive(:sim_info_files).and_return([])

        expect(DeviceSIMHelper.sim_info("device_id")).to eq([])
      end

      it 'should return sim info from device when force -> false and state file ->  present' do
        expect(DeviceSIMHelper).to receive(:sim_info_files).and_return(["android_sim_device_id_0.yml"])
        yaml_detail = { "carrier" => "Jio", "sim_slot" => 1,
                        "imei" => "358793372838886",
                        "iccid" => "8901240270106303746",
                        "phone_number" => "1234567890" }

        expect(YAML).to receive(:load_file).and_return(yaml_detail)

        expect(DeviceSIMHelper.sim_info("device_id")).to eq([
                                                                                { "carrier" => "Jio", "sim_slot" => 1,
                                                                                  "imei" => "358793372838886",
                                                                                  "iccid" => "8901240270106303746",
                                                                                  "phone_number" => "1234567890" }
                                                                              ])
      end

      it 'should return sim info from device when force -> true and state file -> not present' do
        allow(DeviceSIMHelper).to receive(:sim_info_files).and_return([])
        allow(DeviceSIMHelper).to receive(:get_sim_info_from_config).and_return({
          "0" => {
            "carrier" => "Airtel",
            "phone_number" => "1234567890"
          }
        })
        allow(DeviceSIMHelper).to receive(:fetch_sim_prop).and_return('10', '30')

        expect(DeviceSIMHelper.sim_info("device_id", force_update: true)).to eq([
                                                                                { 'carrier' => 'Airtel',
                                                                                  'sim_slot' => 1,
                                                                                  'imei' => '10',
                                                                                  'iccid' => '30',
                                                                                  'phone_number' => '1234567890' }
                                                                              ])
      end

      it 'should return sim info from device when force -> true and state file ->  present' do
        allow(DeviceSIMHelper).to receive(:sim_info_files).and_return(["android_sim_device_id_0.yml"])
        yaml_detail = { "carrier" => "Jio", "sim_slot" => 1,
                        "imei" => "358793372838886",
                        "iccid" => "89012402",
                        "phone_number" => "40" }

        allow(YAML).to receive(:load_file).and_return(yaml_detail)
        allow(DeviceSIMHelper).to receive(:get_sim_info_from_config).and_return({
          "0" => {
            "carrier" => "Jio",
            "phone_number" => "40"
          }
        })
        allow(DeviceSIMHelper).to receive(:fetch_sim_prop).and_return('358793372838886', '89012402')
        expect(DeviceSIMHelper.sim_info("device_id", force_update: true)).to eq([
                                                                                      { 'carrier' => 'Jio',
                                                                                        'sim_slot' => 1,
                                                                                        'imei' => '358793372838886',
                                                                                        'iccid' => '89012402',
                                                                                        'phone_number' => '40' }
                                                                                    ])
      end

      it 'should return sim info from device when force -> true and state file ->  present case 2' do
        allow(DeviceSIMHelper).to receive(:sim_info_files).and_return(["android_sim_device_id_0.yml"])
        yaml_detail = { "carrier" => "Jio", "sim_slot" => 1,
                        "imei" => "3587933728388867",
                        "iccid" => "89012",
                        "phone_number" => "40" }

        allow(YAML).to receive(:load_file).and_return(yaml_detail)
        allow(DeviceSIMHelper).to receive(:get_sim_info_from_config).and_return({
          "0" => {
            "carrier" => "Jio",
            "phone_number" => nil
          }
        })
        allow(DeviceSIMHelper).to receive(:fetch_sim_prop).and_return('3587933728388867', '89012')

        expect(DeviceSIMHelper.sim_info("device_id", force_update: true)).to eq([
                                                                                      { 'carrier' => 'Jio',
                                                                                        'sim_slot' => 1,
                                                                                        'imei' => '3587933728388867',
                                                                                        'iccid' => '89012',
                                                                                        'phone_number' => nil }
                                                                                    ])
      end

      it 'should return sim info from device when force -> true and state file ->  present case 3' do
        allow(DeviceSIMHelper).to receive(:sim_info_files).and_return(["android_sim_device_id_0.yml"])
        yaml_detail = {}
        allow(YAML).to receive(:load_file).and_return(yaml_detail)
        allow(DeviceSIMHelper).to receive(:get_sim_info_from_config).and_return({
          "0" => {
            "carrier" => "Airtel",
            "phone_number" => "1234567890"
          }
        })
        allow(DeviceSIMHelper).to receive(:fetch_sim_prop).and_return('10', '30')
        expect(DeviceSIMHelper.sim_info("device_id", force_update: true)).to eq([
                                                                                      { 'carrier' => 'Airtel',
                                                                                        'sim_slot' => 1,
                                                                                        'imei' => '10',
                                                                                        'iccid' => '30',
                                                                                        'phone_number' => "1234567890" }
                                                                                    ])
      end

      it 'should return sim info from device when force -> true and state file ->  present case 4' do
        allow(DeviceSIMHelper).to receive(:sim_info_files).and_return(["android_sim_device_id_0.yml"])
        yaml_detail = { "carrier" => "Jio",
                        "imei" => "358793372838886",
                        "iccid" => "890",
                        "phone_number" => "" }

        allow(YAML).to receive(:load_file).and_return(yaml_detail)
        allow(DeviceSIMHelper).to receive(:get_sim_info_from_config).and_return({
          "0" => {
            "carrier" => "Jio",
            "phone_number" => "1234567890"
          }
        })
        allow(DeviceSIMHelper).to receive(:fetch_sim_prop).and_return('358793372838886', '890')
        expect(DeviceSIMHelper.sim_info("device_id", force_update: true)).to eq([
                                                                                      { 'carrier' => 'Jio',
                                                                                        'sim_slot' => 1,
                                                                                        'imei' => '358793372838886',
                                                                                        'iccid' => '890',
                                                                                        'phone_number' => "1234567890" }
                                                                                    ])
      end
    end

    context 'if public sim is false' do
      before(:each) do
        expect(DeviceSIMHelper).to receive(:sim?).and_return(false)
      end
      it "return []" do
        expect(DeviceSIMHelper.sim_info("device_id")).to eq([])
      end
    end
  end

  describe '#remove_cache_and_app_data_sms_app' do
    it 'pm clear success' do
      expect(subject).to receive(:sms_app_bundle).and_return("com.samsung.android.messaging")
      expect(adb).to receive(:shell).with(
        "pm clear com.samsung.android.messaging"
      ).and_return(nil)
      expect(subject.clear_sms_app_data).to eq(nil)
    end

    it 'pm clear failed' do
      expect(subject).to receive(:sms_app_bundle).and_return("com.samsung.android.messaging")
      expect(adb).to receive(:shell).with(
        "pm clear com.samsung.android.messaging"
      ).and_raise("some error")
      expect do
        subject.clear_sms_app_data
      end.to raise_error("some error")
    end

    it 'pm list gives raises error' do
      expect(subject).to receive(:sms_app_bundle).and_raise("some error")
      expect do
        subject.clear_sms_app_data
      end.to raise_error("some error")
    end
  end

  describe '#telephony_cleanup' do
    it "should clear any calls, sms data, disable sms, remove notifications, validate and return true" do
      allow(device_owner_manager).to receive(:privileges_enabled?).and_return(true)
      expect(adb).to receive(:shell).with(
        "pm grant com.android.browserstack android.permission.WRITE_CALL_LOG"
      ).and_return("")
      expect(adb).to receive(:shell).with(
        "appops set com.android.browserstack WRITE_SMS allow"
      ).and_return("")
      expect(adb).to receive(:shell).with(
        "am startservice --user 0 -n com.android.browserstack/.services.TelephonyCleanupService"
      ).and_return("")
      expect(subject).to receive(:remove_notifications).and_return(true)
      expect(subject).to receive(:clear_sms_app_data).and_return(true)
      expect(subject).to receive(:validate).exactly(2).times.and_return(true)
      expect(subject).to_not receive(:zombie_push)
      expect(subject).to receive(:send_data_to_eds).and_return(true)
      expect(subject.telephony_cleanup("cleanup")).to eq(true)
    end

    it "should clear any calls, sms data, disable sms, remove notifications, validate and return false" do
      allow(device_owner_manager).to receive(:privileges_enabled?).and_return(true)
      expect(adb).to receive(:shell).with(
        "pm grant com.android.browserstack android.permission.WRITE_CALL_LOG"
      ).and_return("")
      expect(adb).to receive(:shell).with(
        "appops set com.android.browserstack WRITE_SMS allow"
      ).and_return("")
      expect(adb).to receive(:shell).with(
        "am startservice --user 0 -n com.android.browserstack/.services.TelephonyCleanupService"
      ).and_return("")
      expect(subject).to receive(:remove_notifications).and_return(true)
      expect(subject).to receive(:clear_sms_app_data).and_return(true)
      expect(subject).to receive(:validate).and_return(false)
      expect(subject).to receive(:zombie_push)
      expect(subject).to receive(:send_data_to_eds).and_return(true)
      expect(subject.telephony_cleanup("cleanup")).to eq(false)
    end

    it "should clear any calls, sms data, avoid disabling sms if session start, validate and return true" do
      allow(device_owner_manager).to receive(:privileges_enabled?).and_return(true)
      expect(adb).to receive(:shell).with(
        "pm grant com.android.browserstack android.permission.WRITE_CALL_LOG"
      ).and_return("")
      expect(adb).to receive(:shell).with(
        "appops set com.android.browserstack WRITE_SMS allow"
      ).and_return("")
      expect(adb).to receive(:shell).with(
        "am startservice --user 0 -n com.android.browserstack/.services.TelephonyCleanupService"
      ).and_return("")
      expect(subject).to receive(:remove_notifications).and_return(true)
      expect(subject).to receive(:clear_sms_app_data).and_return(true)
      expect(subject).to receive(:validate).exactly(2).times.and_return(true)
      expect(subject).to_not receive(:zombie_push)
      expect(subject).to receive(:send_data_to_eds).and_return(true)
      expect(subject.telephony_cleanup("session_start")).to eq(true)
    end

    it "should clear any calls, sms data, avoid disabling sms if session start, validate and return false" do
      allow(device_owner_manager).to receive(:privileges_enabled?).and_return(true)
      expect(adb).to receive(:shell).with(
        "pm grant com.android.browserstack android.permission.WRITE_CALL_LOG"
      ).and_return("")
      expect(adb).to receive(:shell).with(
        "appops set com.android.browserstack WRITE_SMS allow"
      ).and_return("")
      expect(adb).to receive(:shell).with(
        "am startservice --user 0 -n com.android.browserstack/.services.TelephonyCleanupService"
      ).and_return("")
      expect(subject).to receive(:remove_notifications).and_return(true)
      expect(subject).to receive(:clear_sms_app_data).and_return(true)
      expect(subject).to receive(:validate).and_return(false)
      expect(subject).to receive(:zombie_push)
      expect(subject).to receive(:send_data_to_eds).and_return(true)
      expect(subject.telephony_cleanup("session_start")).to eq(false)
    end
  end

  describe 'disable_sim' do
    let(:adb) { double("mock_adb") }

    context 'if it is a public sim' do
      let(:deviceSIMHelper) { double('deviceSIMHelper') }
      let(:adb) { double("mock_adb") }
      before do
        allow(AndroidToolkit::ADB).to receive(:new).and_return(adb)
      end
      it 'file is not present and we will write to state file' do
        allow(DeviceSIMHelper).to receive(:sim?).and_return(true)
        allow(DeviceSIMHelper).to receive(:new).and_return(deviceSIMHelper)
        allow(deviceSIMHelper).to receive(:toggle_sim_state).and_return(true)
        allow(DeviceSIMHelper).to receive(:sim_info).and_return([])
        allow(adb).to receive(:shell).and_return(true)
        expect(DeviceSIMHelper.disable_sim("device")).to eq(true)
      end

      it 'file is not present and we will write to state file' do
        allow(DeviceSIMHelper).to receive(:sim?).and_return(true)
        allow(DeviceSIMHelper).to receive(:sim_info).and_return([])
        allow(DeviceSIMHelper).to receive(:new).and_return(deviceSIMHelper)
        allow(deviceSIMHelper).to receive(:toggle_sim_state).and_raise("some error")
        expect(DeviceSIMHelper.disable_sim("device")).to eq(nil)
      end
    end
  end

  describe '#cleanup' do
    before(:each) do
      allow(subject).to receive(:validate)
      allow(subject).to receive(:telephony_cleanup)
      allow(subject).to receive(:disable_sms)
      allow(subject).to receive(:send_data_to_eds)
      allow(adb).to receive(:shell)
    end

    context 'when run on non SIM device' do
      before(:each) do
        allow(DeviceSIMHelper).to receive(:sim?).and_return(false)
      end

      it 'should return false if cleanup is ran on non SIM device' do
        expect(subject.cleanup).to eq(false)
      end
    end

    context 'when run on SIM device' do
      before(:each) do
        allow(DeviceSIMHelper).to receive(:sim?).and_return(true)
      end

      context 'when cleanup is successfull' do
        it 'should return true' do
          expect(subject.cleanup).to eq(true)
        end
      end

      context 'when cleanup has failed' do
        before(:each) do
          allow(subject).to receive(:disable_sms).and_raise("some error")
        end

        it 'should raise error if cleanup has failed on SIM device' do
          expect { subject.cleanup }.to raise_error("some error")
        end
      end
    end
  end

  describe '#validate' do
    it "should populate sms present on device and return true when no sms present" do
      expect(adb).to receive(:shell).with(
        "content query --uri content://sms"
      ).and_return("No result found.")
      expect(subject.validate("sms_log_cleanup")).to eq(true)
    end

    it "should populate sms present on device and return false when sms(es) are present" do
      expect(adb).to receive(:shell).with(
        "content query --uri content://sms"
      ).and_return("Row: 0 _id=90, thread_id=60, address=9634224747, person=NULL, date=1676850064870, date_sent=0,
                    protocol=NULL, read=1, status=0, type=2, reply_path_present=...")
      expect(subject.validate("sms_log_cleanup")).to eq(false)
    end

    it "should populate call logs present on device and return true when no call logs present" do
      expect(adb).to receive(:shell).with(
        "content query --uri content://call_log/calls"
      ).and_return("No result found.")
      expect(subject.validate("call_log_cleanup")).to eq(true)
    end

    it "should populate call logs present on device and return false when call logs present" do
      expect(adb).to receive(:shell).with(
        "content query --uri content://call_log/calls"
      ).and_return("Row: 0 formatted_number=NULL, duration=0, subject=NULL,
                    is_call_log_phone_account_migration_pending=0, subscription_id=2, photo_id=0, post_dial_digits=,..")
      expect(subject.validate("call_log_cleanup")).to eq(false)
    end

    it "should return true if sim state is changed to on succesfully" do
      expect(adb).to receive(:shell).with(
        "cat /sdcard/sim_state.prop"
      ).and_return("1")
      expect(subject.validate("toggle_sim_state_on")).to eq(true)
    end

    it "should return true if sim state is changed to off succesfully" do
      expect(adb).to receive(:shell).with(
        "cat /sdcard/sim_state.prop"
      ).and_return("0")
      expect(subject.validate("toggle_sim_state_off")).to eq(true)
    end

    it "should return false if sim state is not changed succesfully" do
      expect(adb).to receive(:shell).with(
        "cat /sdcard/sim_state.prop"
      ).exactly(2).times.and_return("")
      expect(subject.validate("toggle_sim_state_on")).to eq(false)
      expect(subject.validate("toggle_sim_state_off")).to eq(false)
    end
  end
end
