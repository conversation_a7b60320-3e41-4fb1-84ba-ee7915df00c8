GIT
  remote: **************:browserstack/mobile-common.git
  revision: 835ba880edc8595f12f5458cbc38a1248e10b4fe
  branch: master
  specs:
    android_toolkit (0.6.23)
    app_percy_common (0.1.2)
      google-cloud-storage
    aws_s3_wrapper (0.1.2)
      aws-sdk-s3
    browserstack_logger (0.2.6)
      binding_of_caller
      bsenv
    browserstack_utils (0.0.2)
    bsenv (0.0.2)
      dotenv
    device_fork_executor (1.0.3)
    download_bundle_id_config (0.1.0)
    env_middleware (1.0.0)
    interaction_sync_stability_tester (0.1.0)
    mitmproxybuilder (0.0.1)
    mobile_influxdb_client (0.1.4)
    network_helper (0.0.2)
    percy_on_automate_common (0.0.7)
      google-cloud-storage
    reboot (0.1.1)
      diplomat (~> 2.0, >= 2.0.2)
    server_info (1.0.4)
    sirenlogs (0.5.0)
    so_timeout_util (0.2.1)
    static_conf (0.0.4)

GIT
  remote: ssh://**************/browserstack/dwh_ruby.git
  revision: 10a4ecceba7725dc7d1edec94dfc4a52193321ec
  branch: master
  specs:
    bsdwh (0.1.9)

GIT
  remote: ssh://**************/browserstack/restrictions-manager.git
  revision: 1ff0d45e99ca3bed2d8537d94abddb9c8f001ffb
  branch: master
  specs:
    restrictions_manager (0.5.18)
      sequel (~> 5.23)
      sqlite3 (~> 1.4)

GIT
  remote: ssh://**************/browserstack/ruby-hoothoot.git
  revision: e534ae3117ba3418ef7f63072e1b284212a2f72b
  branch: master
  specs:
    hoothoot (0.3.15)

GIT
  remote: ssh://**************/nahi/logger.git
  revision: 9f87939b71938ecc095fe2cc0408eb724c4b310f
  branch: master
  specs:
    logger (1.2.8)

GEM
  remote: http://rubygems.org/
  specs:
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    appium_lib (11.0.0)
      appium_lib_core (~> 4.0)
      nokogiri (~> 1.8, >= 1.8.1)
      tomlrb (~> 1.1)
    appium_lib_core (4.7.1)
      faye-websocket (~> 0.11.0)
      selenium-webdriver (~> 3.14, >= 3.14.1)
    ast (2.4.2)
    aws-eventstream (1.1.1)
    aws-partitions (1.492.0)
    aws-sdk-core (3.119.1)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.239.0)
      aws-sigv4 (~> 1.1)
      jmespath (~> 1.0)
    aws-sdk-kms (1.47.0)
      aws-sdk-core (~> 3, >= 3.119.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.100.0)
      aws-sdk-core (~> 3, >= 3.119.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.1)
    aws-sigv4 (1.2.4)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    bootsnap (1.17.1)
      msgpack (~> 1.2)
    byebug (11.1.3)
    childprocess (3.0.0)
    coderay (1.1.3)
    concurrent-ruby (1.1.10)
    crack (0.4.3)
      safe_yaml (~> 1.0.0)
    debug_inspector (1.2.0)
    declarative (0.0.20)
    deep_merge (1.2.2)
    diff-lcs (1.3)
    digest-crc (0.6.5)
      rake (>= 12.0.0, < 14.0.0)
    diplomat (2.6.4)
      deep_merge (~> 1.2)
      faraday (>= 0.9, < 3.0, != 2.0.0)
    docile (1.3.2)
    dotenv (2.7.6)
    dry-core (0.9.1)
      concurrent-ruby (~> 1.0)
      zeitwerk (~> 2.6)
    dry-monads (1.5.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 0.9, >= 0.9)
      zeitwerk (~> 2.6)
    event_emitter (0.2.6)
    eventmachine (1.2.7)
    faraday (2.8.1)
      base64
      faraday-net_http (>= 2.0, < 3.1)
      ruby2_keywords (>= 0.0.4)
    faraday-net_http (3.0.2)
    faye-websocket (0.11.3)
      eventmachine (>= 0.12.0)
      websocket-driver (>= 0.5.1)
    ffi (1.15.5)
    google-apis-core (0.15.1)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-apis-iamcredentials_v1 (0.22.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-storage_v1 (0.47.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-cloud-core (1.7.1)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.1.1)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.4.0)
    google-cloud-storage (1.52.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-core (~> 0.13)
      google-apis-iamcredentials_v1 (~> 0.18)
      google-apis-storage_v1 (~> 0.38)
      google-cloud-core (~> 1.6)
      googleauth (~> 1.9)
      mini_mime (~> 1.0)
    googleauth (1.11.2)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    hashdiff (0.3.7)
    httpclient (2.8.3)
    io-console (0.5.11)
    irb (1.4.1)
      reline (>= 0.3.0)
    jmespath (1.6.1)
    json (2.6.1)
    jwt (2.9.3)
      base64
    libusb (0.6.4)
      ffi (~> 1.0)
      mini_portile2 (~> 2.1)
    method_source (1.0.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.8)
    msgpack (1.7.2)
    multi_json (1.15.0)
    mustermann (2.0.2)
      ruby2_keywords (~> 0.0.1)
    mutex_m (0.3.0)
    nokogiri (1.15.7)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    os (1.1.4)
    parallel (1.21.0)
    parser (*******)
      ast (~> 2.4.1)
    pry (0.13.1)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.9.0)
      byebug (~> 11.0)
      pry (~> 0.13.0)
    public_suffix (5.1.1)
    puma (3.7.0)
    racc (1.8.1)
    rack (2.2.13)
    rack-protection (2.2.4)
      rack
    rack-test (0.7.0)
      rack (>= 1.0, < 3)
    rainbow (3.1.1)
    rake (13.2.1)
    rdoc (*******)
    regexp_parser (2.2.0)
    reline (0.3.1)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    retriable (3.1.2)
    rexml (3.3.9)
    rmagick (2.16.0)
    rspec (3.7.0)
      rspec-core (~> 3.7.0)
      rspec-expectations (~> 3.7.0)
      rspec-mocks (~> 3.7.0)
    rspec-core (3.7.1)
      rspec-support (~> 3.7.0)
    rspec-expectations (3.7.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.7.0)
    rspec-mocks (3.7.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.7.0)
    rspec-support (3.7.1)
    rubocop (1.25.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml
      rubocop-ast (>= 1.15.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 3.0)
    rubocop-ast (1.15.1)
      parser (>= *******)
    ruby-progressbar (1.11.0)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    safe_yaml (1.0.4)
    selenium-webdriver (3.142.7)
      childprocess (>= 0.5, < 4.0)
      rubyzip (>= 1.2.2)
    sequel (5.23.0)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.17.0)
      docile (~> 1.1)
      json (>= 1.8, < 3)
      simplecov-html (~> 0.10.0)
    simplecov-html (0.10.2)
    sinatra (2.2.4)
      mustermann (~> 2.0)
      rack (~> 2.2)
      rack-protection (= 2.2.4)
      tilt (~> 2.0)
    sqlite3 (1.4.2)
    tilt (2.1.0)
    tomlrb (1.3.0)
    trailblazer-option (0.1.2)
    uber (0.1.0)
    unicode-display_width (2.1.0)
    webmock (3.4.2)
      addressable (>= 2.3.6)
      crack (>= 0.3.2)
      hashdiff
    websocket (1.2.11)
    websocket-client-simple (0.3.0)
      event_emitter
      websocket
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.6.1)

PLATFORMS
  ruby

DEPENDENCIES
  android_toolkit!
  app_percy_common!
  appium_lib (= 11.0.0)
  aws-eventstream (= 1.1.1)
  aws-partitions (= 1.492.0)
  aws-sdk-core (= 3.119.1)
  aws_s3_wrapper!
  bootsnap (= 1.17.1)
  browserstack_logger!
  browserstack_utils!
  bsdwh!
  device_fork_executor!
  dotenv (~> 2.7.6)
  download_bundle_id_config!
  dry-monads
  env_middleware!
  google-cloud-storage (~> 1.52)
  hoothoot!
  interaction_sync_stability_tester!
  irb
  jmespath (~> 1.6.1)
  json (= 2.6.1)
  libusb (~> 0.6.4)
  logger!
  mitmproxybuilder!
  mobile_influxdb_client!
  network_helper!
  nokogiri (~> 1.15.0)
  percy_on_automate_common!
  pry
  pry-byebug
  puma (= 3.7.0)
  rack-test (= 0.7)
  rdoc (= *******)
  reboot!
  restrictions_manager!
  rmagick (~> 2.15, >= 2.15.4)
  rspec (~> 3.7)
  rubocop
  rubyzip
  sequel (~> 5.23.0)
  server_info!
  simplecov
  sinatra (= 2.2.4)
  sirenlogs!
  so_timeout_util!
  sqlite3 (~> 1.4.0)
  static_conf!
  webmock (~> 3.4)
  websocket-client-simple (= 0.3.0)

BUNDLED WITH
   2.2.33
